{"name": "jumpers-for-goalposts-backend", "version": "1.0.0", "author": "<PERSON>", "private": true, "type": "module", "main": "infrastructure/index.ts", "packageManager": "npm@10.5.0", "scripts": {"generate-types": "node scripts/generate-types.js", "build": "npm run generate-types && node scripts/build.cjs", "lint": "eslint . --config .eslintrc.cjs", "lint:fix": "eslint . --config .eslintrc.cjs --resolve-plugins-relative-to . --fix", "prettier": "prettier --check ./src", "prettier:fix": "prettier --write ./src", "test": "cross-env LOG_LEVEL=silent vitest run --coverage", "test:ci": "cross-env LOG_LEVEL=silent CI=true vitest run --coverage", "test:watch": "cross-env LOG_LEVEL=silent vitest", "test:ui": "cross-env LOG_LEVEL=silent vitest --ui", "typecheck": "tsc --noEmit", "offline": "cross-env NODE_ENV=development sls offline start", "localstack:up": "docker-compose up -d", "localstack:down": "docker-compose down", "test-script": "node --experimental-specifier-resolution=node --loader ./src/testing/loader.js src/testing/testLambda.ts", "mikro-orm": "mikro-orm-esm", "migration:generate": "mikro-orm-esm migration:create", "migration:run": "mikro-orm-esm migration:up", "migration:revert": "mikro-orm-esm migration:down", "truncate-tables": "node --loader ts-node/esm scripts/truncate-tables.ts", "deploy": "npm run migration:run && npm run build", "test-match-engine": "node --experimental-specifier-resolution=node --loader ./src/testing/loader.js ./src/testing/testMatchEngine.ts"}, "devDependencies": {"@faker-js/faker": "^9.7.0", "@mikro-orm/entity-generator": "^6.4.15", "@swc/cli": "^0.6.0", "@swc/core": "^1.10.15", "@tsconfig/node-lts": ">= 18", "@types/aws-lambda": "^8.10.147", "@types/lodash-es": "^4.17.12", "@types/mjml": "^4.7.4", "@types/node": "^20", "@types/pg": "^8.11.11", "@typescript-eslint/eslint-plugin": ">= 7", "@typescript-eslint/parser": ">= 7", "@vitest/coverage-v8": "^3.1.4", "@vitest/ui": "^3.1.4", "cross-env": "^7.0.3", "esbuild": "^0.25.2", "esbuild-node-externals": "^1.18.0", "esbuild-plugin-swc": "^1.0.1", "esbuild-plugin-tsc": "^0.5.0", "eslint": ">= 8", "eslint-config-prettier": ">= 9", "eslint-import-resolver-typescript": ">= 3", "eslint-plugin-import": ">= 2", "eslint-plugin-vitest": "^0.5.4", "get-tsconfig": "^4.7.3", "glob": "^11.0.1", "interface-forge": "^2.1.0", "json-schema-to-typescript": "^13.1.2", "lodash-es": "^4.17.21", "prettier": ">= 3", "prettier-plugin-organize-imports": ">= 3", "swc-loader": "^0.2.6", "ts-node": "^10.4.0", "tsconfig-paths": "^4.2.0", "typescript": "^5.8.3", "vitest": "^3.1.4", "webpack-cli": "^6.0.1"}, "dependencies": {"@aws-lambda-powertools/logger": "^2.19.1", "@aws-lambda-powertools/tracer": "^2.19.1", "@aws-sdk/client-dynamodb": "^3.744.0", "@aws-sdk/client-ses": "^3.744.0", "@aws-sdk/client-sns": "^3.744.0", "@aws-sdk/client-sqs": "^3.758.0", "@aws-sdk/lib-dynamodb": "^3.744.0", "@middy/core": "^6.0.0", "@middy/event-normalizer": "^6.0.0", "@middy/http-cors": "^6.0.0", "@middy/http-event-normalizer": "^6.0.0", "@middy/http-header-normalizer": "^6.0.0", "@middy/http-json-body-parser": "^6.0.0", "@middy/http-security-headers": "^6.0.0", "@middy/input-output-logger": "^6.0.0", "@middy/validator": "^6.0.0", "@mikro-orm/cli": "^6.4.15", "@mikro-orm/core": "^6.4.15", "@mikro-orm/migrations": "^6.4.15", "@mikro-orm/postgresql": "^6.4.15", "@mikro-orm/reflection": "^6.4.15", "@pulumi/aws": "^6.68.0", "@pulumi/aws-apigateway": "^2.6.2", "@pulumi/awsx": "^2.21.0", "@pulumi/pulumi": "^3.113.0", "ajv": "^8.17.1", "aws-jwt-verify": "^5.0.0", "aws-lambda": "^1.0.7", "axios": "^1.9.0", "date-fns": "^4.1.0", "dotenv": "^16.4.7", "expo-server-sdk": "^3.15.0", "google-auth-library": "^9.15.1", "obscenity": "^0.4.3", "pg": "^8.14.1", "pg-query-stream": "^4.8.1", "reflect-metadata": "^0.2.2", "serverless-offline": "^14.4.0", "terser-webpack-plugin": "^5.3.11", "uuid": "^11.1.0"}, "ts-node": {"esm": true}, "mikro-orm": {"useTsNode": true, "configPaths": ["./mikro-orm.config.ts"]}}