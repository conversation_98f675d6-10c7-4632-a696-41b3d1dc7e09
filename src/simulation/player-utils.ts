import { Player } from '@/entities/Player.js';
import {
  calculatePlayerValue,
  generatePlayerAttributes,
} from '@/functions/generate/player-attributes.js';
import { CurrentAttributes } from '@/model/player.js';
import { GamePlayer } from '@/simulation/types.js';
import { TeamRepository } from '@/storage-interface/teams/index.js';
import { seededRandomIntInRange, setAndReturnSeededRandom } from '@/utils/seeded-random.js';

export function sortPlayersByPosition(
  gameworldId: string,
  teamId: string,
  teamRepository: TeamRepository,
  players: Player[]
): GamePlayer[] {
  const calculateScore = (player: Player, attributes: (keyof CurrentAttributes)[]): number => {
    return (
      attributes.reduce((sum, attr) => {
        const key = `${attr}Current` as keyof typeof player.attributes;
        return sum + (player.attributes![key] as number);
      }, 0) / attributes.length
    );
  };

  // Exclude players who are injured or suspended
  const eligiblePlayers = players.filter(
    (player) =>
      (!player.injuredUntil || player.injuredUntil < Date.now()) && player.suspendedForGames === 0
  );

  // Separate players based on energy levels. Only use low energy players if there are not enough high energy players.
  const highEnergyPlayers = eligiblePlayers.filter((player) => player.energy >= 30);
  const lowEnergyPlayers = eligiblePlayers.filter((player) => player.energy < 30);

  // If there are not enough players to field a team, generate trialists
  if (highEnergyPlayers.length + lowEnergyPlayers.length < 16) {
    const trialistsNeeded = 16 - (highEnergyPlayers.length + lowEnergyPlayers.length);
    const trialists = generateTrialist(gameworldId, teamId, teamRepository, trialistsNeeded);
    highEnergyPlayers.push(...trialists);
  }

  // Ensure we have at least 16 players, filling with low energy players if necessary
  if (highEnergyPlayers.length < 16) {
    const additionalLowEnergyPlayersNeeded = 16 - highEnergyPlayers.length;
    const sortedLowEnergyPlayers = lowEnergyPlayers.sort((a, b) => b.energy - a.energy);
    highEnergyPlayers.push(...sortedLowEnergyPlayers.slice(0, additionalLowEnergyPlayersNeeded));
  }

  const sortedPlayers = highEnergyPlayers;

  const positionAttributes = {
    goalkeeper: ['reflexes', 'positioning', 'shotStopping'] as (keyof CurrentAttributes)[],
    defender: ['tackling', 'marking', 'heading'] as (keyof CurrentAttributes)[],
    midfielder: ['passing', 'vision', 'ballControl'] as (keyof CurrentAttributes)[],
    attacker: ['finishing', 'pace', 'crossing'] as (keyof CurrentAttributes)[],
  };

  // Calculate scores for each player based on their position attributes
  const playerScores = sortedPlayers.map((player) => ({
    player,
    scores: {
      goalkeeper: calculateScore(player, positionAttributes.goalkeeper),
      defender: calculateScore(player, positionAttributes.defender),
      midfielder: calculateScore(player, positionAttributes.midfielder),
      attacker: calculateScore(player, positionAttributes.attacker),
    },
  }));

  const result: Player[] = new Array(highEnergyPlayers.length) as Player[];

  playerScores.sort((a, b) => b.scores.goalkeeper - a.scores.goalkeeper);
  result[0] = playerScores[0]!.player;
  playerScores.shift();

  playerScores.sort((a, b) => b.scores.defender - a.scores.defender);
  for (let i = 1; i <= 4; i++) {
    result[i] = playerScores[0]!.player;
    playerScores.shift();
  }

  playerScores.sort((a, b) => b.scores.midfielder - a.scores.midfielder);
  for (let i = 5; i <= 8; i++) {
    result[i] = playerScores[0]!.player;
    playerScores.shift();
  }

  playerScores.sort((a, b) => b.scores.attacker - a.scores.attacker);
  for (let i = 9; i <= 10; i++) {
    result[i] = playerScores[0]!.player;
    playerScores.shift();
  }

  if (playerScores.length > 0) {
    playerScores.forEach((playerScore, index) => {
      result[11 + index] = playerScore.player;
    });
  }

  return result.map((player) => ({
    player: player,
    stats: {
      yellowCards: 0,
      redCards: 0,
      passesCompleted: 0,
      passesAttempted: 0,
      successfulBallCarries: 0,
      ballCarriesAttempted: 0,
      shots: 0,
      shotsOnTarget: 0,
      goals: 0,
      saves: 0,
      tackles: 0,
      fouls: 0,
    },
    isInjured: false,
    hasBeenSubbed: false,
  }));
}

export function getAttributeValue(player: Player, attribute: keyof CurrentAttributes): number {
  const key = `${attribute}Current` as keyof typeof player.attributes;
  return player.attributes![key] as number;
}

export function generateTrialist(
  gameworldId: string,
  teamId: string,
  teamRepository: TeamRepository,
  count: number = 5
): Player[] {
  const trialists: Player[] = [];

  for (let i = 0; i < count; i++) {
    const player = new Player();
    player.playerId = 'trialist';
    player.gameworldId = gameworldId;
    player.team = teamRepository.createFromPK(teamId);
    player.seed = setAndReturnSeededRandom();
    player.age = seededRandomIntInRange(16, 18);
    player.firstName = 'A.';
    player.surname = 'Trialist';
    player.attributes = generatePlayerAttributes(1, 1, 2, []);
    player.value = Number(calculatePlayerValue(player).toFixed(2));
    player.energy = 100;
    player.lastMatchPlayed = 0;
    player.suspendedForGames = 0;

    trialists.push(player);
  }

  return trialists;
}
