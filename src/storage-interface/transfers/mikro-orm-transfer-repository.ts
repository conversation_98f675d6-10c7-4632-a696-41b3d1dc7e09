import { BidHistory } from '@/entities/BidHistory.js';
import { Player } from '@/entities/Player.js';
import { Team } from '@/entities/Team.js';
import { TransferListedPlayer } from '@/entities/TransferListedPlayer.js';
import { TransferRequest } from '@/entities/TransferRequest.js';
import { MikroOrmService } from '@/storage-interface/mikro-orm-service.js';
import {
  Auction,
  DBTransferListedPlayer,
  DBTransferRequest,
  PaginatedTransferListedPlayers,
  TransferRepository,
} from '@/storage-interface/transfers/transfer-repository.interface.js';
import { logger } from '@/utils/logger.js';
import { Reference } from '@mikro-orm/core';

/**
 * MikroORM implementation of the TransferRepository
 */
export class MikroOrmTransferRepository implements TransferRepository {
  constructor(private mikroOrmService: MikroOrmService) {}

  flush(): Promise<void> {
    const em = this.mikroOrmService.getEntityManager();
    return em.flush();
  }

  /**
   * Submit a transfer offer for a player
   * @param playerId The ID of the player to transfer
   * @param value The offer value
   * @param buyerTeamId The ID of the buying team
   * @param sellerTeamId The ID of the selling team
   * @returns The created or updated transfer request
   */
  async submitOffer(
    playerId: string,
    value: number,
    buyerTeamId: string,
    sellerTeamId: string
  ): Promise<TransferRequest> {
    try {
      const em = this.mikroOrmService.getEntityManager();

      // Check if there's already an existing transfer request for this player and buyer
      const existingRequest = await em.findOne(TransferRequest, {
        player: playerId,
        buyer: buyerTeamId,
      });

      if (existingRequest) {
        // Update the existing transfer request with new value and date
        existingRequest.value = value;
        existingRequest.date = Date.now();
        existingRequest.seller = em.getReference(Team, sellerTeamId);

        // Reset counter offer fields since this is a new offer
        existingRequest.counterOfferTime = 0;
        existingRequest.counterOfferValue = 0;

        await em.persistAndFlush(existingRequest);

        logger.debug('Updated existing transfer request:', {
          transferRequestId: existingRequest.id,
          playerId,
          buyerTeamId,
          sellerTeamId,
          newValue: value,
        });

        return existingRequest;
      }

      // Create a new transfer request if none exists
      const transferRequest = new TransferRequest();
      transferRequest.date = Date.now();
      transferRequest.value = value;
      transferRequest.player = em.getReference(Player, playerId);
      transferRequest.buyer = em.getReference(Team, buyerTeamId);
      transferRequest.seller = em.getReference(Team, sellerTeamId);

      // Save the transfer request
      await em.persistAndFlush(transferRequest);

      logger.debug('Created new transfer request:', {
        transferRequestId: transferRequest.id,
        playerId,
        buyerTeamId,
        sellerTeamId,
        value,
      });

      return transferRequest;
    } catch (error) {
      logger.error('Failed to submit transfer offer:', {
        error,
        playerId,
        buyerTeamId,
        sellerTeamId,
      });
      throw error;
    }
  }

  /**
   * Get a transfer request by ID
   * @param transferRequestId The ID of the transfer request
   * @returns The transfer request, or null if not found
   */
  async getTransferRequest(transferRequestId: string): Promise<TransferRequest | null> {
    try {
      const em = this.mikroOrmService.getEntityManager();
      return await em.findOne(
        TransferRequest,
        { id: transferRequestId },
        {
          populate: ['player', 'buyer', 'seller', 'buyer.manager', 'seller.manager'],
        }
      );
    } catch (error) {
      logger.error('Failed to get transfer request:', { error, transferRequestId });
      throw error;
    }
  }

  /**
   * Get all transfer requests for sent to AI managers
   * @returns Array of transfer requests
   */
  async getTransferRequestsToAI(): Promise<DBTransferRequest[]> {
    try {
      const em = this.mikroOrmService.getEntityManager();
      // First get the IDs with the date comparison
      const qb = em
        .createQueryBuilder(TransferRequest, 'tr')
        .select('tr.id')
        .leftJoin('tr.seller', 's')
        .leftJoin('s.manager', 'm')
        .where('tr.date > tr.counter_offer_time')
        .andWhere('m.manager_id IS NULL');

      const ids = await qb.execute();

      // Then fetch the full entities with populations
      return await em.find(
        TransferRequest,
        {
          id: { $in: ids.map((row) => row.id) },
        },
        {
          populate: [
            'seller',
            'seller.players',
            'buyer',
            'buyer.manager',
            'buyer.manager.email',
            'buyer.manager.notificationPreferences',
            'buyer.manager.pushToken',
            'player',
          ],
        }
      );
    } catch (error) {
      logger.error('Failed to get transfer requests by buyer:', { error });
      throw error;
    }
  }

  /**
   * Get all transfer requests for a team as seller
   * @param teamId The ID of the selling team
   * @returns Array of transfer requests
   */
  async getTransferRequestsBySeller(teamId: string): Promise<TransferRequest[]> {
    try {
      const em = this.mikroOrmService.getEntityManager();
      return await em.find(
        TransferRequest,
        { seller: teamId },
        {
          populate: ['player', 'buyer'],
        }
      );
    } catch (error) {
      logger.error('Failed to get transfer requests by seller:', { error, teamId });
      throw error;
    }
  }

  /**
   * Get all transfer requests for a team as buyer
   * @param teamId The ID of the buying team
   * @returns Array of transfer requests
   */
  async getTransferRequestsByBuyer(teamId: string): Promise<TransferRequest[]> {
    try {
      const em = this.mikroOrmService.getEntityManager();
      return await em.find(
        TransferRequest,
        { buyer: teamId },
        {
          populate: ['player', 'seller'],
        }
      );
    } catch (error) {
      logger.error('Failed to get transfer requests by buyer:', { error, teamId });
      throw error;
    }
  }

  /**
   * Get all transfer requests for a player
   * @param playerId The ID of the player
   * @returns Array of transfer requests
   */
  async getTransferRequestsByPlayer(playerId: string): Promise<TransferRequest[]> {
    try {
      const em = this.mikroOrmService.getEntityManager();
      return await em.find(
        TransferRequest,
        { player: playerId },
        {
          populate: ['buyer', 'seller'],
        }
      );
    } catch (error) {
      logger.error('Failed to get transfer requests by player:', { error, playerId });
      throw error;
    }
  }

  /**
   * Update a transfer request with a counter offer
   * @param transferRequest The transfer request
   * @param counterOfferValue The value of the counter offer
   * @param flush Should we flush the database
   * @returns The updated transfer request
   */
  async submitCounterOffer(
    transferRequest: TransferRequest,
    counterOfferValue: number,
    flush = true
  ): Promise<TransferRequest> {
    try {
      const em = this.mikroOrmService.getEntityManager();

      // Update with counter offer details
      transferRequest.counterOfferTime = Date.now();
      transferRequest.counterOfferValue = counterOfferValue;

      // Save the updated transfer request
      if (flush) {
        await em.persistAndFlush(transferRequest);
      } else {
        em.persist(transferRequest);
      }

      return transferRequest;
    } catch (error) {
      logger.error('Failed to submit counter offer:', { error, id: transferRequest.id });
      throw error;
    }
  }

  /**
   * Add a transfer listed player
   * @param player The player to add to the transfer list
   */
  addTransferListedPlayer(player: Player): Promise<void> {
    try {
      const em = this.mikroOrmService.getEntityManager();
      const transferListedPlayer = new TransferListedPlayer();
      transferListedPlayer.player = em.getReference(Player, player.playerId);
      transferListedPlayer.gameworldId = player.gameworldId;
      transferListedPlayer.auctionStartPrice = player.value;
      transferListedPlayer.auctionCurrentPrice = player.value;
      transferListedPlayer.auctionEndTime = Date.now() + 24 * 60 * 60 * 1000; // 24 hours from now
      em.persist(transferListedPlayer);

      player.isTransferListed = true;
      em.persist(player);

      return em.flush();
    } catch (error) {
      logger.error('Failed to add transfer listed player:', { error, player });
      throw error;
    }
  }

  /**
   * Delete a transfer request
   * @param transferRequestId The ID of the transfer request to delete
   */
  async deleteTransferRequest(transferRequestId: string): Promise<void> {
    try {
      const em = this.mikroOrmService.getEntityManager();

      // Find the transfer request
      const transferRequest = await em.findOne(TransferRequest, { id: transferRequestId });
      if (!transferRequest) {
        throw new Error(`Transfer request not found: ${transferRequestId}`);
      }

      // Delete the transfer request
      await em.removeAndFlush(transferRequest);
    } catch (error) {
      logger.error('Failed to delete transfer request:', { error, transferRequestId });
      throw error;
    }
  }

  private roundDownToPowerOf10(value: number): number {
    if (value <= 0) return 0;
    const magnitude = Math.floor(Math.log10(value));
    return Math.floor(value / Math.pow(10, magnitude)) * Math.pow(10, magnitude);
  }

  async submitBid(
    player: string,
    maxBid: number,
    myTeam: string,
    repositories?: any
  ): Promise<{ maxBid: number; highestBidder: boolean }> {
    try {
      const em = this.mikroOrmService.getEntityManager();

      logger.debug('Submitting bid', { player, maxBid, myTeam });

      // Find the transfer listed player
      const transferListedPlayer = await em.findOne(
        TransferListedPlayer,
        { player: em.getReference(Player, player) },
        {
          populate: ['bidHistory', 'bidHistory.team', 'player'],
        }
      );
      if (!transferListedPlayer) {
        throw new Error(`Transfer listed player not found: ${player}`);
      }

      // Track the previous highest bidder before processing the new bid
      const previousBids = await em.find(
        BidHistory,
        { transferListing: transferListedPlayer.id },
        {
          orderBy: { maximumBid: 'DESC' },
          populate: ['team'],
        }
      );
      const previousHighestBidder = previousBids.length > 0 ? previousBids[0] : null;

      // is our bid above the current auction price
      if (maxBid <= transferListedPlayer.auctionCurrentPrice) {
        return Promise.resolve({
          maxBid: transferListedPlayer.auctionCurrentPrice,
          highestBidder: false,
        });
      }

      // Check if the team has already made a bid
      const existingBid = await em.findOne(BidHistory, {
        transferListing: transferListedPlayer.id,
        team: em.getReference(Team, myTeam),
      });

      if (existingBid) {
        // Update the existing bid
        existingBid.maximumBid = maxBid;
        existingBid.bidTime = Date.now();
        em.persist(existingBid);
      } else {
        // Create a new bid
        const newBid = new BidHistory();
        newBid.transferListing = em.getReference(TransferListedPlayer, transferListedPlayer.id);
        newBid.team = em.getReference(Team, myTeam);
        newBid.maximumBid = maxBid;
        newBid.bidTime = Date.now();
        em.persist(newBid);
      }

      // Get all bids for this auction, sorted by maximum bid (highest first)
      const allBids = await em.find(
        BidHistory,
        { transferListing: transferListedPlayer.id },
        {
          orderBy: { maximumBid: 'DESC' },
          populate: ['team'],
        }
      );

      // Calculate the new auction price using eBay-style logic
      const { newAuctionPrice, isHighestBidder } = this.calculateEbayStylePrice(
        allBids,
        transferListedPlayer.auctionStartPrice,
        transferListedPlayer.auctionCurrentPrice,
        myTeam
      );

      // Update the auction current price
      transferListedPlayer.auctionCurrentPrice = newAuctionPrice;

      // Save the updated transfer-listed player
      await em.persistAndFlush(transferListedPlayer);

      // Send outbid notifications if someone was outbid
      await this.sendOutbidNotifications(
        previousHighestBidder!,
        allBids,
        transferListedPlayer,
        newAuctionPrice,
        myTeam,
        repositories
      );

      logger.debug('Bid submitted successfully', {
        player,
        maxBid,
        myTeam,
        newAuctionPrice,
        isHighestBidder,
      });

      return Promise.resolve({
        maxBid: newAuctionPrice,
        highestBidder: isHighestBidder,
      });
    } catch (error) {
      logger.error('Failed to submit bid:', { error, player, maxBid, myTeam });
      throw error;
    }
  }

  /**
   * Calculate the new auction price using eBay-style proxy bidding logic
   * @param bids All bids sorted by maximum bid (highest first)
   * @param startPrice The auction start price
   * @param currentPrice The current auction price
   * @param biddingTeam The team that just placed a bid
   * @returns Object with new auction price and whether the bidding team is highest bidder
   */
  private calculateEbayStylePrice(
    bids: BidHistory[],
    startPrice: number,
    currentPrice: number,
    biddingTeam: string
  ): { newAuctionPrice: number; isHighestBidder: boolean } {
    if (bids.length === 0) {
      return { newAuctionPrice: startPrice, isHighestBidder: false };
    }

    if (bids.length === 1) {
      // First bid - auction stays at start price
      return {
        newAuctionPrice: startPrice,
        isHighestBidder: bids[0]!.team.teamId === biddingTeam,
      };
    }

    // Get the two highest bids
    const highestBid = bids[0]!;
    const secondHighestBid = bids[1]!;

    // Calculate bid increment (10% of current price, rounded down to power of 10)
    const increment = this.roundDownToPowerOf10(currentPrice) * 0.1;

    let newAuctionPrice: number;
    const isHighestBidder = highestBid.team.teamId === biddingTeam;

    if (highestBid.maximumBid === secondHighestBid.maximumBid) {
      // Equal bids - price stays at the bid amount
      newAuctionPrice = highestBid.maximumBid;
    } else {
      // Calculate price based on second highest bid + increment
      const calculatedPrice = secondHighestBid.maximumBid + increment;
      // Cap at the highest bidder's maximum
      newAuctionPrice = Math.min(calculatedPrice, highestBid.maximumBid);
    }

    return { newAuctionPrice, isHighestBidder };
  }

  /**
   * Send outbid notifications to users who were outbid by the new bid
   */
  private async sendOutbidNotifications(
    previousHighestBidder: BidHistory | null,
    allBids: BidHistory[],
    transferListedPlayer: TransferListedPlayer,
    newAuctionPrice: number,
    biddingTeam: string,
    repositories?: any
  ): Promise<void> {
    try {
      // Don't send notifications if we don't have repositories
      if (!repositories?.managerRepository) {
        logger.debug('Skipping outbid notifications - no repositories provided');
        return;
      }

      // Get the current highest bidder after the new bid
      const currentHighestBidder = allBids.length > 0 ? allBids[0] : null;

      // If there was a previous highest bidder and they're not the current highest bidder
      if (
        previousHighestBidder &&
        currentHighestBidder &&
        previousHighestBidder.team.teamId !== currentHighestBidder.team.teamId &&
        previousHighestBidder.team.teamId !== biddingTeam
      ) {
        await this.sendOutbidNotificationToTeam(
          previousHighestBidder.team.teamId,
          transferListedPlayer,
          newAuctionPrice,
          currentHighestBidder.team.teamName,
          repositories
        );
      }
    } catch (error) {
      logger.error('Failed to send outbid notifications', {
        error,
        transferListedPlayer: transferListedPlayer.id,
        biddingTeam,
      });
      // Don't throw - notification failure shouldn't break the bidding process
    }
  }

  /**
   * Send outbid notification to a specific team
   */
  private async sendOutbidNotificationToTeam(
    teamId: string,
    transferListedPlayer: TransferListedPlayer,
    newAuctionPrice: number,
    outbiddingTeamName: string,
    repositories: any
  ): Promise<void> {
    try {
      // Import NotificationManager here to avoid circular dependencies
      const { NotificationManager } = await import(
        '@/services/notifications/NotificationManager.js'
      );

      // Find the manager for this team
      const manager = await repositories.managerRepository.getManagerByTeamId(teamId);
      if (!manager) {
        logger.warn('Manager not found for outbid notification', { teamId });
        return;
      }

      // Create notification manager instance and load preferences
      const notificationManager = NotificationManager.getInstance();
      notificationManager.assignManagerPreferences(manager, repositories);

      // Get player name
      const playerName = `${transferListedPlayer.player.firstName} ${transferListedPlayer.player.surname}`;

      // Send the outbid notification
      await notificationManager.outbidNotification(playerName, newAuctionPrice, outbiddingTeamName);

      logger.debug('Outbid notification sent successfully', {
        teamId,
        managerId: manager.managerId,
        playerName,
        newAuctionPrice,
      });
    } catch (error) {
      logger.error('Failed to send outbid notification to team', {
        error,
        teamId,
        transferListedPlayer: transferListedPlayer.id,
      });
    }
  }

  async getCompletedAuctions(): Promise<Auction[]> {
    try {
      const em = this.mikroOrmService.getEntityManager();

      // Find expired auctions
      const players = await em.find(
        TransferListedPlayer,
        {
          auctionEndTime: { $lt: Date.now() },
        },
        {
          populate: [
            'player',
            'player.team',
            'player.team.manager',
            'bidHistory',
            'bidHistory.team',
            'bidHistory.team.manager',
          ],
        }
      );

      return Promise.resolve(players);
    } catch (error) {
      logger.error('Failed to get completed auctions:', { error });
      throw error;
    }
  }

  async deleteTransferListedPlayer(auctionId: string): Promise<void> {
    try {
      const em = this.mikroOrmService.getEntityManager();

      // First find the entity with its relationships
      const transferListedPlayer = await em.findOne(
        TransferListedPlayer,
        { id: auctionId },
        { populate: ['bidHistory', 'player'] }
      );

      if (transferListedPlayer) {
        // Update the player's transfer listed status
        const player = transferListedPlayer.player;
        if (player) {
          player.isTransferListed = false;
          em.persist(player);
        }

        // Remove only the transfer listed player entity
        // This will cascade delete the bid history due to orphanRemoval: true
        await em.removeAndFlush(transferListedPlayer);

        logger.info('Removed transfer listed player and bid history', { auctionId });
      } else {
        logger.warn('Transfer listed player not found for deletion:', { auctionId });
      }
    } catch (error) {
      logger.error('Failed to delete transfer listed player:', { error, auctionId });
      throw error;
    }
  }

  async updateTransferListedPlayer(transferListedPlayer: TransferListedPlayer): Promise<void> {
    try {
      const em = this.mikroOrmService.getEntityManager();
      await em.upsert(TransferListedPlayer, transferListedPlayer);
      return em.flush();
    } catch (error) {
      logger.error('Failed to update transfer listed player:', { error, transferListedPlayer });
      throw error;
    }
  }

  async getActiveAuctions(gameworldId: string): Promise<number> {
    try {
      const em = this.mikroOrmService.getEntityManager();
      return await em.count(TransferListedPlayer, {
        auctionEndTime: { $gt: Date.now() },
        gameworldId,
      });
    } catch (error) {
      logger.error('Failed to count active auctions:', { error });
      throw error;
    }
  }
  /**
   * Get all transfer listed players for a gameworld
   * @param gameworldId The gameworld ID
   * @param limit Optional limit for pagination
   * @param lastEvaluatedKey Optional key for pagination
   * @param sortBy Optional field to sort by (auctionCurrentPrice or auctionEndTime)
   * @param sortDirection Optional sort direction (ASC or DESC)
   * @param minPrice Optional minimum price filter
   * @param maxPrice Optional maximum price filter
   * @returns Paginated result with transfer listed players and pagination info
   */
  async getTransferListedPlayers(
    gameworldId: string,
    limit?: number,
    lastEvaluatedKey?: string,
    sortBy?: string,
    sortDirection?: string,
    minPrice?: number,
    maxPrice?: number
  ): Promise<PaginatedTransferListedPlayers> {
    try {
      const em = this.mikroOrmService.getEntityManager();

      // Parse the lastEvaluatedKey if provided
      let offset = 0;
      if (lastEvaluatedKey) {
        try {
          const decodedKey = JSON.parse(Buffer.from(lastEvaluatedKey, 'base64').toString());
          offset = decodedKey.offset || 0;
        } catch (error) {
          logger.warn('Invalid lastEvaluatedKey provided, starting from beginning', {
            lastEvaluatedKey,
            error,
          });
          offset = 0;
        }
      }

      // Set default limit if not provided
      const queryLimit = limit || 25;

      // Build the filter criteria
      const filterCriteria: any = { gameworldId, auctionEndTime: { $gt: Date.now() } };

      // Add price range filters if provided
      if (minPrice !== undefined) {
        filterCriteria.auctionCurrentPrice = {
          ...(filterCriteria.auctionCurrentPrice || {}),
          $gte: minPrice,
        };
      }

      if (maxPrice !== undefined) {
        filterCriteria.auctionCurrentPrice = {
          ...(filterCriteria.auctionCurrentPrice || {}),
          $lte: maxPrice,
        };
      }

      // Build the ordering criteria
      let orderBy: Record<string, 'ASC' | 'DESC'> = { createdAt: 'ASC', id: 'ASC' }; // Default ordering

      // Override with custom sort if provided
      if (sortBy && ['auctionCurrentPrice', 'auctionEndTime'].includes(sortBy)) {
        const direction = sortDirection === 'DESC' ? 'DESC' : 'ASC'; // Default to ASC if not specified
        orderBy = { [sortBy]: direction, id: 'ASC' }; // Include id for consistent ordering
      }

      // Query one extra item to determine if there are more pages
      const players = await em.find(TransferListedPlayer, filterCriteria, {
        limit: queryLimit + 1,
        offset,
        populate: ['player', 'player.attributes', 'bidHistory', 'bidHistory.team'],
        orderBy,
      });

      // Check if there are more pages
      const hasMorePages = players.length > queryLimit;
      const resultPlayers = hasMorePages ? players.slice(0, queryLimit) : players;

      // Generate next page token if there are more pages
      let nextLastEvaluatedKey: string | undefined;
      if (hasMorePages) {
        const nextOffset = offset + queryLimit;
        const nextKey = { offset: nextOffset };
        nextLastEvaluatedKey = Buffer.from(JSON.stringify(nextKey)).toString('base64');
      }

      logger.debug('Retrieved transfer listed players with pagination', {
        gameworldId,
        limit: queryLimit,
        offset,
        sortBy,
        sortDirection,
        minPrice,
        maxPrice,
        returnedCount: resultPlayers.length,
        hasMorePages,
      });

      return {
        players: resultPlayers,
        lastEvaluatedKey: nextLastEvaluatedKey,
      };
    } catch (error) {
      logger.error('Failed to get transfer listed players:', {
        error,
        gameworldId,
        limit,
        lastEvaluatedKey,
      });
      throw error;
    }
  }

  /**
   * Get transfer listed players that a specific team has already bid on
   * @param teamId The ID of the team
   * @param gameworldId The gameworld ID
   * @returns Array of transfer listed players that the team has bid on
   */
  async getTransferListedPlayersWithTeamBids(
    teamId: string,
    gameworldId: string
  ): Promise<DBTransferListedPlayer[]> {
    try {
      const em = this.mikroOrmService.getEntityManager();

      // Find transfer listed players where the team has placed a bid
      const players = await em.find(
        TransferListedPlayer,
        {
          gameworldId,
          auctionEndTime: { $gt: Date.now() },
          bidHistory: { team: Reference.createFromPK(Team, teamId) },
        },
        {
          populate: ['player', 'player.attributes', 'bidHistory', 'bidHistory.team'],
        }
      );

      return Promise.resolve(players);
    } catch (error) {
      logger.error('Failed to get transfer listed players with team bids:', {
        error,
        teamId,
        gameworldId,
      });
      throw error;
    }
  }
}
