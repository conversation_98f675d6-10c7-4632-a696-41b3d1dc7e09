import { Fixture } from '@/entities/Fixture.js';
import { FixtureDetails, MatchEvent, MatchStats } from '@/model/fixture.js';
import DynamoDbService from '@/services/database/dynamo/dynamo-db-service.js';
import { MikroOrmService } from '@/storage-interface/mikro-orm-service.js';
import { logger } from '@/utils/logger.js';
import { FilterQuery, Loaded } from '@mikro-orm/core';
import { SqlEntityManager } from '@mikro-orm/postgresql';
import { IFixtureRepository } from './fixture-repository.interface.js';

export class FixtureRepository implements IFixtureRepository {
  private dynamoDb: DynamoDbService;

  constructor(private mikroOrmService: MikroOrmService) {
    this.dynamoDb = new DynamoDbService();
  }

  getEntityManager(): SqlEntityManager {
    return this.mikroOrmService.getEntityManager();
  }
  createFromPK(id: string): Fixture {
    return this.mikroOrmService.getEntityManager().getReference(Fixture, id);
  }

  async batchInsertFixtures(fixtures: Fixture[]): Promise<void> {
    try {
      const em = this.mikroOrmService.getEntityManager();
      for (const fixture of fixtures) {
        em.persist(fixture);
      }
      await em.flush();
    } catch (error) {
      logger.error('Failed to create fixtures:', { error });
      throw error;
    }
  }

  async getFixture(fixtureId: string): Promise<FixtureDetails | null> {
    return this.dynamoDb.get<FixtureDetails>(process.env.FIXTURE_DETAIL_TABLE_NAME!, {
      fixtureId,
    });
  }

  async getFixturesByLeague(gameworldId: string, leagueId: string): Promise<Fixture[]> {
    const em = this.mikroOrmService.getEntityManager();
    const where: FilterQuery<Fixture> = { gameworldId, league: { id: leagueId } };
    return em.find(Fixture, where);
  }

  async getFixturesByTeam(
    gameworldId: string,
    teamId: string
  ): Promise<Loaded<Fixture, 'homeTeam' | 'awayTeam'>[]> {
    const em = this.mikroOrmService.getEntityManager();
    const where: FilterQuery<Fixture> = {
      gameworldId,
      $or: [{ homeTeam: { teamId } }, { awayTeam: { teamId } }],
    };
    const fixtures = await em.find(Fixture, where, {
      orderBy: { date: 'ASC' },
      populate: ['homeTeam', 'awayTeam'],
    });

    return fixtures;
  }

  async getDueFixtures(gameworldId?: string, leagueId?: string): Promise<Fixture[]> {
    const em = this.mikroOrmService.getEntityManager();
    const currentTime = Date.now();

    const where: FilterQuery<Fixture> = {
      date: { $lte: currentTime },
      played: false,
    };

    if (gameworldId) {
      where.gameworldId = gameworldId;
    }

    if (leagueId) {
      where.league = { id: leagueId };
    }

    return em.find(Fixture, where, {
      orderBy: { date: 'ASC' },
      populate: ['homeTeam', 'awayTeam'],
    });
  }

  async getAllUnplayedFixtures(gameworldId?: string, leagueId?: string): Promise<Fixture[]> {
    const em = this.mikroOrmService.getEntityManager();

    const where: FilterQuery<Fixture> = {
      played: false,
    };

    if (gameworldId) {
      where.gameworldId = gameworldId;
    }

    if (leagueId) {
      where.league = { id: leagueId };
    }

    return em.find(Fixture, where, {
      orderBy: { date: 'ASC' },
      populate: ['homeTeam', 'awayTeam'],
    });
  }

  async updateFixtureResult(
    gameworldId: string,
    leagueId: string,
    fixtureId: string,
    seed: number,
    stats: MatchStats,
    events: MatchEvent[]
  ): Promise<void> {
    const em = this.mikroOrmService.getEntityManager();
    const fixture = await em.findOne(Fixture, {
      gameworldId,
      league: { id: leagueId },
      fixtureId,
    });

    if (!fixture) {
      throw new Error(`Fixture not found: ${gameworldId}, ${leagueId}, ${fixtureId}`);
    }

    fixture.score = stats.score;
    fixture.scorers = stats.scorers;
    fixture.played = true;
    fixture.seed = seed;
    fixture.simulatedAt = Date.now();

    await em.persistAndFlush(fixture);

    await this.dynamoDb.insert<FixtureDetails>(process.env.FIXTURE_DETAIL_TABLE_NAME!, {
      fixtureId,
      gameworldId,
      leagueId,
      homeTeamId: fixture.homeTeam.teamId,
      homeTeamName: fixture.homeTeam.teamName,
      awayTeamId: fixture.awayTeam.teamId,
      awayTeamName: fixture.awayTeam.teamName,
      date: fixture.date,
      stats,
      events,
    });
  }

  async deleteCompletedFixtures(gameworldId: string, leagueId: string): Promise<void> {
    const em = this.mikroOrmService.getEntityManager();
    await em.nativeDelete(Fixture, {
      gameworldId,
      league: { id: leagueId },
      played: true,
    });

    // Query the GSI for matching items in DynamoDB
    const result = await this.dynamoDb.query<FixtureDetails>(
      process.env.FIXTURE_DETAIL_TABLE_NAME!,
      {
        hashKey: { name: 'gameworldId', value: gameworldId },
        rangeKey: { name: 'leagueId', value: leagueId },
      },
      'gameworldId_leagueId_index'
    );

    // Prepare array of keys for batch delete
    const keys = result.items.map((item) => ({ fixtureId: item.fixtureId }));

    // Batch delete the items in chunks of 25
    for (let i = 0; i < keys.length; i += 25) {
      await this.dynamoDb.batchDelete(
        process.env.FIXTURE_DETAIL_TABLE_NAME!,
        keys.slice(i, i + 25)
      );
    }
  }
}
