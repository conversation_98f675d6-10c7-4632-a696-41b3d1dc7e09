import { PlayerAttributes } from '@/entities/PlayerAttributes.js';
import { CurrentAttributes } from '@/model/player.js';

/**
 * Converts a player's full attributes (with current and potential values)
 * to just the current attributes.
 *
 * @param attributes The full attributes object with current and potential values
 * @returns An object containing only the current attribute values
 */
export function extractCurrentAttributes(attributes: PlayerAttributes): CurrentAttributes {
  return {
    isGoalkeeper: attributes.isGoalkeeper,
    reflexes: attributes.reflexesCurrent,
    positioning: attributes.positioningCurrent,
    shotStopping: attributes.shotStoppingCurrent,
    tackling: attributes.tacklingCurrent,
    marking: attributes.markingCurrent,
    heading: attributes.headingCurrent,
    finishing: attributes.finishingCurrent,
    pace: attributes.paceCurrent,
    crossing: attributes.crossingCurrent,
    passing: attributes.passingCurrent,
    vision: attributes.visionCurrent,
    ballControl: attributes.ballControlCurrent,
    stamina: attributes.stamina,
  };
}
