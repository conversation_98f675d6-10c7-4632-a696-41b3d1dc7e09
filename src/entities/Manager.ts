import { Entity, <PERSON>To<PERSON>ne, <PERSON><PERSON>ey, Property, type Rel, Unique } from '@mikro-orm/core';
import { Team } from './Team.js';

// Define notification categories
export enum NotificationCategory {
  TRANSFERS = 'transfers',
  TRAINING = 'training',
  PRE_MATCH = 'preMatch',
  SCOUTING_RESULTS = 'scoutingResults',
  POST_MATCH = 'postMatch',
  SYSTEM_ANNOUNCEMENTS = 'announcements',
}

// Define notification channels
export enum NotificationChannel {
  PUSH = 'push',
  EMAIL = 'email',
}

// Notification preferences type
export type NotificationPreferences = {
  [key in NotificationCategory]?: {
    [channel in NotificationChannel]?: boolean;
  };
};

@Entity({ tableName: 'manager' })
@Unique({ name: 'manager_id_key', properties: ['managerId'] })
export class Manager {
  @PrimaryKey({ type: 'uuid' })
  managerId!: string;

  @Property({ type: 'bigint' })
  createdAt!: number;

  @Property({ type: 'bigint' })
  lastActive!: number;

  @Property({ length: 100, nullable: true })
  firstName?: string;

  @Property({ length: 100, nullable: true })
  lastName?: string;

  @Property({ type: 'string', nullable: true })
  email?: string;

  @OneToOne({ entity: () => Team, fieldName: 'team_id', nullable: true })
  team?: Rel<Team>;

  @Property({ type: 'uuid', nullable: true })
  gameworldId?: string;

  @Property({ type: 'integer' })
  scoutTokens: number = 2;

  @Property({ type: 'integer' })
  superScoutTokens: number = 0;

  @Property({ type: 'integer' })
  magicSponges: number = 1;

  @Property({ type: 'integer' })
  cardAppeals: number = 0;

  @Property({ type: 'integer' })
  trainingBoosts: number = 0;

  @Property({ type: 'json', nullable: true })
  notificationPreferences?: NotificationPreferences = {};

  @Property({ nullable: true, type: 'string' })
  pushToken?: string;

  @Property({ type: 'integer' })
  loginStreak: number = 0;
}
