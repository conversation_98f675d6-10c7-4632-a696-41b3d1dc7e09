{"namespaces": ["public"], "name": "public", "tables": [{"columns": {"id": {"name": "id", "type": "uuid", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "uuid"}, "gameworld_id": {"name": "gameworld_id", "type": "uuid", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "uuid"}, "team_id": {"name": "team_id", "type": "uuid", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "uuid"}}, "name": "available_team", "schema": "public", "indexes": [{"keyName": "available_team_gameworld_id_team_id_unique", "columnNames": ["gameworld_id", "team_id"], "composite": true, "constraint": true, "primary": false, "unique": true}, {"keyName": "available_team_pkey", "columnNames": ["id"], "composite": false, "constraint": true, "primary": true, "unique": true}], "checks": [], "foreignKeys": {}, "nativeEnums": {}}, {"columns": {"id": {"name": "id", "type": "uuid", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "uuid_generate_v4()", "mappedType": "uuid"}, "end_date": {"name": "end_date", "type": "bigint", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "bigint"}, "highest_manageable_tier": {"name": "highest_manageable_tier", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "3", "mappedType": "integer"}}, "name": "gameworld", "schema": "public", "indexes": [{"keyName": "gameworld_id_unique", "columnNames": ["id"], "composite": false, "constraint": true, "primary": false, "unique": true}, {"keyName": "gameworld_pkey", "columnNames": ["id"], "composite": false, "constraint": true, "primary": true, "unique": true}], "checks": [], "foreignKeys": {}, "nativeEnums": {}}, {"columns": {"id": {"name": "id", "type": "uuid", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "uuid_generate_v4()", "mappedType": "uuid"}, "gameworld_id": {"name": "gameworld_id", "type": "uuid", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "uuid"}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(100)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 100, "mappedType": "string"}, "tier": {"name": "tier", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "integer"}, "parent_league_id": {"name": "parent_league_id", "type": "uuid", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "uuid"}}, "name": "league", "schema": "public", "indexes": [{"columnNames": ["tier"], "composite": false, "keyName": "idx_leagues_tier", "constraint": false, "primary": false, "unique": false}, {"columnNames": ["parent_league_id"], "composite": false, "keyName": "idx_leagues_parent", "constraint": false, "primary": false, "unique": false}, {"keyName": "league_id_unique", "columnNames": ["id"], "composite": false, "constraint": true, "primary": false, "unique": true}, {"keyName": "league_pkey", "columnNames": ["id"], "composite": false, "constraint": true, "primary": true, "unique": true}], "checks": [], "foreignKeys": {"league_gameworld_id_foreign": {"constraintName": "league_gameworld_id_foreign", "columnNames": ["gameworld_id"], "localTableName": "public.league", "referencedColumnNames": ["id"], "referencedTableName": "public.gameworld", "updateRule": "cascade"}, "league_parent_league_id_foreign": {"constraintName": "league_parent_league_id_foreign", "columnNames": ["parent_league_id"], "localTableName": "public.league", "referencedColumnNames": ["id"], "referencedTableName": "public.league", "deleteRule": "set null", "updateRule": "cascade"}}, "nativeEnums": {}}, {"columns": {"parent_league_id": {"name": "parent_league_id", "type": "uuid", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "uuid"}, "child_league_id": {"name": "child_league_id", "type": "uuid", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "uuid"}}, "name": "league_children", "schema": "public", "indexes": [{"keyName": "league_children_pkey", "columnNames": ["parent_league_id", "child_league_id"], "composite": true, "constraint": true, "primary": true, "unique": true}], "checks": [], "foreignKeys": {"league_children_parent_league_id_foreign": {"constraintName": "league_children_parent_league_id_foreign", "columnNames": ["parent_league_id"], "localTableName": "public.league_children", "referencedColumnNames": ["id"], "referencedTableName": "public.league", "deleteRule": "cascade", "updateRule": "cascade"}, "league_children_child_league_id_foreign": {"constraintName": "league_children_child_league_id_foreign", "columnNames": ["child_league_id"], "localTableName": "public.league_children", "referencedColumnNames": ["id"], "referencedTableName": "public.league", "deleteRule": "cascade", "updateRule": "cascade"}}, "nativeEnums": {}}, {"columns": {"league_id": {"name": "league_id", "type": "uuid", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "uuid"}, "promotion_spots": {"name": "promotion_spots", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "integer"}, "relegation_spots": {"name": "relegation_spots", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "integer"}, "team_count": {"name": "team_count", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "integer"}, "maximum_prize": {"name": "maximum_prize", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "0", "mappedType": "integer"}, "minimum_prize": {"name": "minimum_prize", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "0", "mappedType": "integer"}}, "name": "league_rules", "schema": "public", "indexes": [{"keyName": "league_rules_pkey", "columnNames": ["league_id"], "composite": false, "constraint": true, "primary": true, "unique": true}], "checks": [], "foreignKeys": {"league_rules_league_id_foreign": {"constraintName": "league_rules_league_id_foreign", "columnNames": ["league_id"], "localTableName": "public.league_rules", "referencedColumnNames": ["id"], "referencedTableName": "public.league", "deleteRule": "cascade", "updateRule": "cascade"}}, "nativeEnums": {}}, {"columns": {"team_id": {"name": "team_id", "type": "uuid", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "uuid"}, "gameworld_id": {"name": "gameworld_id", "type": "uuid", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "uuid"}, "league_id": {"name": "league_id", "type": "uuid", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "uuid"}, "tier": {"name": "tier", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "integer"}, "team_name": {"name": "team_name", "type": "<PERSON><PERSON><PERSON>(100)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 100, "mappedType": "string"}, "balance": {"name": "balance", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "300000", "mappedType": "integer"}, "played": {"name": "played", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "0", "mappedType": "integer"}, "points": {"name": "points", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "0", "mappedType": "integer"}, "goals_for": {"name": "goals_for", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "0", "mappedType": "integer"}, "goals_against": {"name": "goals_against", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "0", "mappedType": "integer"}, "wins": {"name": "wins", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "0", "mappedType": "integer"}, "draws": {"name": "draws", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "0", "mappedType": "integer"}, "losses": {"name": "losses", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "0", "mappedType": "integer"}, "selection_order": {"name": "selection_order", "type": "text[]", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "array"}, "training_level": {"name": "training_level", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "1", "mappedType": "integer"}}, "name": "team", "schema": "public", "indexes": [{"columnNames": ["league_id"], "composite": false, "keyName": "idx_teams_league", "constraint": false, "primary": false, "unique": false}, {"keyName": "teams_gameworld_id_team_id_key", "columnNames": ["gameworld_id", "team_id"], "composite": true, "constraint": true, "primary": false, "unique": true}, {"keyName": "team_pkey", "columnNames": ["team_id"], "composite": false, "constraint": true, "primary": true, "unique": true}], "checks": [], "foreignKeys": {"team_league_id_foreign": {"constraintName": "team_league_id_foreign", "columnNames": ["league_id"], "localTableName": "public.team", "referencedColumnNames": ["id"], "referencedTableName": "public.league", "updateRule": "cascade"}}, "nativeEnums": {}}, {"columns": {"request_id": {"name": "request_id", "type": "uuid", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "uuid_generate_v4()", "mappedType": "uuid"}, "gameworld_id": {"name": "gameworld_id", "type": "uuid", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "uuid"}, "team_team_id": {"name": "team_team_id", "type": "uuid", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "uuid"}, "type": {"name": "type", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "enumItems": ["player", "team", "league"], "mappedType": "enum"}, "target_id": {"name": "target_id", "type": "<PERSON><PERSON><PERSON>(255)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 255, "mappedType": "string"}, "process_after": {"name": "process_after", "type": "bigint", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "bigint"}, "processed_at": {"name": "processed_at", "type": "bigint", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "bigint"}, "processed": {"name": "processed", "type": "boolean", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "false", "mappedType": "boolean"}, "created_at": {"name": "created_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}}, "name": "scouting_requests", "schema": "public", "indexes": [{"keyName": "scouting_requests_request_id_unique", "columnNames": ["request_id"], "composite": false, "constraint": true, "primary": false, "unique": true}, {"keyName": "scouting_requests_pkey", "columnNames": ["request_id"], "composite": false, "constraint": true, "primary": true, "unique": true}], "checks": [], "foreignKeys": {"scouting_requests_team_team_id_foreign": {"constraintName": "scouting_requests_team_team_id_foreign", "columnNames": ["team_team_id"], "localTableName": "public.scouting_requests", "referencedColumnNames": ["team_id"], "referencedTableName": "public.team", "updateRule": "cascade"}}, "nativeEnums": {}}, {"columns": {"player_id": {"name": "player_id", "type": "uuid", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "uuid"}, "gameworld_id": {"name": "gameworld_id", "type": "uuid", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "uuid"}, "team_id": {"name": "team_id", "type": "uuid", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "uuid"}, "age": {"name": "age", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "integer"}, "seed": {"name": "seed", "type": "bigint", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "bigint"}, "first_name": {"name": "first_name", "type": "<PERSON><PERSON><PERSON>(255)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 255, "mappedType": "string"}, "surname": {"name": "surname", "type": "<PERSON><PERSON><PERSON>(255)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 255, "mappedType": "string"}, "value": {"name": "value", "type": "numeric(15,2)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "precision": 15, "scale": 2, "mappedType": "decimal"}, "energy": {"name": "energy", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "integer"}, "last_match_played": {"name": "last_match_played", "type": "bigint", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "bigint"}, "injured_until": {"name": "injured_until", "type": "bigint", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "bigint"}, "suspended_for_games": {"name": "suspended_for_games", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "integer"}, "is_transfer_listed": {"name": "is_transfer_listed", "type": "boolean", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "false", "mappedType": "boolean"}, "retiring_at_end_of_season": {"name": "retiring_at_end_of_season", "type": "boolean", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "false", "mappedType": "boolean"}}, "name": "players", "schema": "public", "indexes": [{"keyName": "players_gameworld_id_player_id_key", "columnNames": ["gameworld_id", "player_id"], "composite": true, "constraint": true, "primary": false, "unique": true}, {"keyName": "players_pkey", "columnNames": ["player_id"], "composite": false, "constraint": true, "primary": true, "unique": true}], "checks": [], "foreignKeys": {"players_team_id_foreign": {"constraintName": "players_team_id_foreign", "columnNames": ["team_id"], "localTableName": "public.players", "referencedColumnNames": ["team_id"], "referencedTableName": "public.team", "deleteRule": "set null", "updateRule": "cascade"}}, "nativeEnums": {}}, {"columns": {"team_team_id": {"name": "team_team_id", "type": "uuid", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "uuid"}, "player_player_id": {"name": "player_player_id", "type": "uuid", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "uuid"}, "gameworld_id": {"name": "gameworld_id", "type": "uuid", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "uuid"}, "scouted_at": {"name": "scouted_at", "type": "bigint", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "bigint"}}, "name": "scouted_players", "schema": "public", "indexes": [{"keyName": "scouted_players_gameworld_id_team_team_id_player__1708a_unique", "columnNames": ["gameworld_id", "team_team_id", "player_player_id"], "composite": true, "constraint": true, "primary": false, "unique": true}, {"keyName": "scouted_players_pkey", "columnNames": ["team_team_id", "player_player_id"], "composite": true, "constraint": true, "primary": true, "unique": true}], "checks": [], "foreignKeys": {"scouted_players_team_team_id_foreign": {"constraintName": "scouted_players_team_team_id_foreign", "columnNames": ["team_team_id"], "localTableName": "public.scouted_players", "referencedColumnNames": ["team_id"], "referencedTableName": "public.team", "updateRule": "cascade"}, "scouted_players_player_player_id_foreign": {"constraintName": "scouted_players_player_player_id_foreign", "columnNames": ["player_player_id"], "localTableName": "public.scouted_players", "referencedColumnNames": ["player_id"], "referencedTableName": "public.players", "deleteRule": "cascade"}}, "nativeEnums": {}}, {"columns": {"player_id": {"name": "player_id", "type": "uuid", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "uuid"}, "yellow_cards": {"name": "yellow_cards", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "0", "mappedType": "integer"}, "red_cards": {"name": "red_cards", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "0", "mappedType": "integer"}, "passes_completed": {"name": "passes_completed", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "0", "mappedType": "integer"}, "passes_attempted": {"name": "passes_attempted", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "0", "mappedType": "integer"}, "successful_ball_carries": {"name": "successful_ball_carries", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "0", "mappedType": "integer"}, "ball_carries_attempted": {"name": "ball_carries_attempted", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "0", "mappedType": "integer"}, "shots": {"name": "shots", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "0", "mappedType": "integer"}, "shots_on_target": {"name": "shots_on_target", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "0", "mappedType": "integer"}, "goals": {"name": "goals", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "0", "mappedType": "integer"}, "saves": {"name": "saves", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "0", "mappedType": "integer"}, "tackles": {"name": "tackles", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "0", "mappedType": "integer"}, "fouls": {"name": "fouls", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "0", "mappedType": "integer"}}, "name": "player_overall_stats", "schema": "public", "indexes": [{"keyName": "player_overall_stats_pkey", "columnNames": ["player_id"], "composite": false, "constraint": true, "primary": true, "unique": true}], "checks": [], "foreignKeys": {"player_overall_stats_player_id_foreign": {"constraintName": "player_overall_stats_player_id_foreign", "columnNames": ["player_id"], "localTableName": "public.player_overall_stats", "referencedColumnNames": ["player_id"], "referencedTableName": "public.players", "deleteRule": "cascade", "updateRule": "cascade"}}, "nativeEnums": {}}, {"columns": {"player_id": {"name": "player_id", "type": "uuid", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "uuid"}, "is_goalkeeper": {"name": "is_goalkeeper", "type": "boolean", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "false", "mappedType": "boolean"}, "reflexes_current": {"name": "reflexes_current", "type": "float", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 4, "mappedType": "float"}, "reflexes_potential": {"name": "reflexes_potential", "type": "float", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 4, "mappedType": "float"}, "positioning_current": {"name": "positioning_current", "type": "float", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 4, "mappedType": "float"}, "positioning_potential": {"name": "positioning_potential", "type": "float", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 4, "mappedType": "float"}, "shot_stopping_current": {"name": "shot_stopping_current", "type": "float", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 4, "mappedType": "float"}, "shot_stopping_potential": {"name": "shot_stopping_potential", "type": "float", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 4, "mappedType": "float"}, "tackling_current": {"name": "tackling_current", "type": "float", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 4, "mappedType": "float"}, "tackling_potential": {"name": "tackling_potential", "type": "float", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 4, "mappedType": "float"}, "marking_current": {"name": "marking_current", "type": "float", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 4, "mappedType": "float"}, "marking_potential": {"name": "marking_potential", "type": "float", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 4, "mappedType": "float"}, "heading_current": {"name": "heading_current", "type": "float", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 4, "mappedType": "float"}, "heading_potential": {"name": "heading_potential", "type": "float", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 4, "mappedType": "float"}, "finishing_current": {"name": "finishing_current", "type": "float", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 4, "mappedType": "float"}, "finishing_potential": {"name": "finishing_potential", "type": "float", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 4, "mappedType": "float"}, "pace_current": {"name": "pace_current", "type": "float", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 4, "mappedType": "float"}, "pace_potential": {"name": "pace_potential", "type": "float", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 4, "mappedType": "float"}, "crossing_current": {"name": "crossing_current", "type": "float", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 4, "mappedType": "float"}, "crossing_potential": {"name": "crossing_potential", "type": "float", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 4, "mappedType": "float"}, "passing_current": {"name": "passing_current", "type": "float", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 4, "mappedType": "float"}, "passing_potential": {"name": "passing_potential", "type": "float", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 4, "mappedType": "float"}, "vision_current": {"name": "vision_current", "type": "float", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 4, "mappedType": "float"}, "vision_potential": {"name": "vision_potential", "type": "float", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 4, "mappedType": "float"}, "ball_control_current": {"name": "ball_control_current", "type": "float", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 4, "mappedType": "float"}, "ball_control_potential": {"name": "ball_control_potential", "type": "float", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 4, "mappedType": "float"}, "stamina": {"name": "stamina", "type": "real", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "precision": 2, "mappedType": "float"}}, "name": "player_attributes", "schema": "public", "indexes": [{"keyName": "player_attributes_pkey", "columnNames": ["player_id"], "composite": false, "constraint": true, "primary": true, "unique": true}], "checks": [], "foreignKeys": {"player_attributes_player_id_foreign": {"constraintName": "player_attributes_player_id_foreign", "columnNames": ["player_id"], "localTableName": "public.player_attributes", "referencedColumnNames": ["player_id"], "referencedTableName": "public.players", "deleteRule": "cascade"}}, "nativeEnums": {}}, {"columns": {"manager_id": {"name": "manager_id", "type": "uuid", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "uuid"}, "created_at": {"name": "created_at", "type": "bigint", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "bigint"}, "last_active": {"name": "last_active", "type": "bigint", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "bigint"}, "first_name": {"name": "first_name", "type": "<PERSON><PERSON><PERSON>(100)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 100, "mappedType": "string"}, "last_name": {"name": "last_name", "type": "<PERSON><PERSON><PERSON>(100)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 100, "mappedType": "string"}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(255)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 255, "mappedType": "string"}, "team_id": {"name": "team_id", "type": "uuid", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "uuid"}, "gameworld_id": {"name": "gameworld_id", "type": "uuid", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "uuid"}, "scout_tokens": {"name": "scout_tokens", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "2", "mappedType": "integer"}, "super_scout_tokens": {"name": "super_scout_tokens", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "0", "mappedType": "integer"}, "magic_sponges": {"name": "magic_sponges", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "1", "mappedType": "integer"}, "card_appeals": {"name": "card_appeals", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "0", "mappedType": "integer"}, "training_boosts": {"name": "training_boosts", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "0", "mappedType": "integer"}, "notification_preferences": {"name": "notification_preferences", "type": "jsonb", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "json"}, "push_token": {"name": "push_token", "type": "<PERSON><PERSON><PERSON>(255)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 255, "mappedType": "string"}, "login_streak": {"name": "login_streak", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "0", "mappedType": "integer"}}, "name": "manager", "schema": "public", "indexes": [{"columnNames": ["team_id"], "composite": false, "keyName": "manager_team_id_unique", "constraint": true, "primary": false, "unique": true}, {"keyName": "manager_id_key", "columnNames": ["manager_id"], "composite": false, "constraint": true, "primary": false, "unique": true}, {"keyName": "manager_pkey", "columnNames": ["manager_id"], "composite": false, "constraint": true, "primary": true, "unique": true}], "checks": [], "foreignKeys": {"manager_team_id_foreign": {"constraintName": "manager_team_id_foreign", "columnNames": ["team_id"], "localTableName": "public.manager", "referencedColumnNames": ["team_id"], "referencedTableName": "public.team", "deleteRule": "set null", "updateRule": "cascade"}}, "nativeEnums": {}}, {"columns": {"fixture_id": {"name": "fixture_id", "type": "uuid", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "uuid"}, "gameworld_id": {"name": "gameworld_id", "type": "uuid", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "uuid"}, "league_id": {"name": "league_id", "type": "uuid", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "uuid"}, "home_team_team_id": {"name": "home_team_team_id", "type": "uuid", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "uuid"}, "away_team_team_id": {"name": "away_team_team_id", "type": "uuid", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "uuid"}, "date": {"name": "date", "type": "bigint", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "bigint"}, "played": {"name": "played", "type": "boolean", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "false", "mappedType": "boolean"}, "simulated_at": {"name": "simulated_at", "type": "bigint", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "bigint"}, "seed": {"name": "seed", "type": "bigint", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "bigint"}, "score": {"name": "score", "type": "text[]", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "array"}, "scorers": {"name": "scorers", "type": "jsonb", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "json"}}, "name": "fixture", "schema": "public", "indexes": [{"keyName": "fixture_gameworld_id_league_id_fixture_id_unique", "columnNames": ["gameworld_id", "league_id", "fixture_id"], "composite": true, "constraint": true, "primary": false, "unique": true}, {"keyName": "fixture_pkey", "columnNames": ["fixture_id"], "composite": false, "constraint": true, "primary": true, "unique": true}], "checks": [], "foreignKeys": {"fixture_league_id_foreign": {"constraintName": "fixture_league_id_foreign", "columnNames": ["league_id"], "localTableName": "public.fixture", "referencedColumnNames": ["id"], "referencedTableName": "public.league", "updateRule": "cascade"}, "fixture_home_team_team_id_foreign": {"constraintName": "fixture_home_team_team_id_foreign", "columnNames": ["home_team_team_id"], "localTableName": "public.fixture", "referencedColumnNames": ["team_id"], "referencedTableName": "public.team", "updateRule": "cascade"}, "fixture_away_team_team_id_foreign": {"constraintName": "fixture_away_team_team_id_foreign", "columnNames": ["away_team_team_id"], "localTableName": "public.fixture", "referencedColumnNames": ["team_id"], "referencedTableName": "public.team", "updateRule": "cascade"}}, "nativeEnums": {}}, {"columns": {"player_id": {"name": "player_id", "type": "uuid", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "uuid"}, "fixture_id": {"name": "fixture_id", "type": "uuid", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "uuid"}, "fixture_fixture_id": {"name": "fixture_fixture_id", "type": "uuid", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "uuid"}, "yellow_cards": {"name": "yellow_cards", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "0", "mappedType": "integer"}, "red_cards": {"name": "red_cards", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "0", "mappedType": "integer"}, "passes_completed": {"name": "passes_completed", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "0", "mappedType": "integer"}, "passes_attempted": {"name": "passes_attempted", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "0", "mappedType": "integer"}, "successful_ball_carries": {"name": "successful_ball_carries", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "0", "mappedType": "integer"}, "ball_carries_attempted": {"name": "ball_carries_attempted", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "0", "mappedType": "integer"}, "shots": {"name": "shots", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "0", "mappedType": "integer"}, "shots_on_target": {"name": "shots_on_target", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "0", "mappedType": "integer"}, "goals": {"name": "goals", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "0", "mappedType": "integer"}, "saves": {"name": "saves", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "0", "mappedType": "integer"}, "tackles": {"name": "tackles", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "0", "mappedType": "integer"}, "fouls": {"name": "fouls", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "0", "mappedType": "integer"}}, "name": "player_match_history", "schema": "public", "indexes": [{"keyName": "player_match_history_pkey", "columnNames": ["player_id", "fixture_id"], "composite": true, "constraint": true, "primary": true, "unique": true}], "checks": [], "foreignKeys": {"player_match_history_player_id_foreign": {"constraintName": "player_match_history_player_id_foreign", "columnNames": ["player_id"], "localTableName": "public.player_match_history", "referencedColumnNames": ["player_id"], "referencedTableName": "public.players", "deleteRule": "cascade"}, "player_match_history_fixture_fixture_id_foreign": {"constraintName": "player_match_history_fixture_fixture_id_foreign", "columnNames": ["fixture_fixture_id"], "localTableName": "public.player_match_history", "referencedColumnNames": ["fixture_id"], "referencedTableName": "public.fixture", "deleteRule": "cascade"}}, "nativeEnums": {}}, {"columns": {"id": {"name": "id", "type": "uuid", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "uuid_generate_v4()", "mappedType": "uuid"}, "team_team_id": {"name": "team_team_id", "type": "uuid", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "uuid"}, "slot_index": {"name": "slot_index", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "integer"}, "player_player_id": {"name": "player_player_id", "type": "uuid", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "uuid"}, "attribute": {"name": "attribute", "type": "<PERSON><PERSON><PERSON>(255)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 255, "mappedType": "string"}, "start_value": {"name": "start_value", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "integer"}, "assigned_at": {"name": "assigned_at", "type": "bigint", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "bigint"}}, "name": "team_training_slots", "schema": "public", "indexes": [{"keyName": "team_training_slots_pkey", "columnNames": ["id"], "composite": false, "constraint": true, "primary": true, "unique": true}], "checks": [], "foreignKeys": {"team_training_slots_team_team_id_foreign": {"constraintName": "team_training_slots_team_team_id_foreign", "columnNames": ["team_team_id"], "localTableName": "public.team_training_slots", "referencedColumnNames": ["team_id"], "referencedTableName": "public.team", "updateRule": "cascade"}, "team_training_slots_player_player_id_foreign": {"constraintName": "team_training_slots_player_player_id_foreign", "columnNames": ["player_player_id"], "localTableName": "public.team_training_slots", "referencedColumnNames": ["player_id"], "referencedTableName": "public.players", "deleteRule": "set null", "updateRule": "cascade"}}, "nativeEnums": {}}, {"columns": {"id": {"name": "id", "type": "uuid", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "uuid_generate_v4()", "mappedType": "uuid"}, "gameworld_id": {"name": "gameworld_id", "type": "uuid", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "uuid"}, "team_id": {"name": "team_id", "type": "uuid", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "uuid"}, "date": {"name": "date", "type": "bigint", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "bigint"}, "amount": {"name": "amount", "type": "numeric(15,2)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "precision": 15, "scale": 2, "mappedType": "decimal"}, "type": {"name": "type", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "details": {"name": "details", "type": "jsonb", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "json"}}, "name": "transactions", "schema": "public", "indexes": [{"keyName": "transactions_pkey", "columnNames": ["id"], "composite": false, "constraint": true, "primary": true, "unique": true}], "checks": [], "foreignKeys": {"transactions_team_id_foreign": {"constraintName": "transactions_team_id_foreign", "columnNames": ["team_id"], "localTableName": "public.transactions", "referencedColumnNames": ["team_id"], "referencedTableName": "public.team", "updateRule": "cascade"}}, "nativeEnums": {}}, {"columns": {"id": {"name": "id", "type": "uuid", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "uuid_generate_v4()", "mappedType": "uuid"}, "gameworld_id": {"name": "gameworld_id", "type": "uuid", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "uuid"}, "player_player_id": {"name": "player_player_id", "type": "uuid", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "uuid"}, "auction_start_price": {"name": "auction_start_price", "type": "numeric(15,2)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "precision": 15, "scale": 2, "mappedType": "decimal"}, "auction_current_price": {"name": "auction_current_price", "type": "numeric(15,2)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "precision": 15, "scale": 2, "mappedType": "decimal"}, "auction_end_time": {"name": "auction_end_time", "type": "bigint", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "bigint"}, "auction_listing_counter": {"name": "auction_listing_counter", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "0", "mappedType": "integer"}, "created_at": {"name": "created_at", "type": "bigint", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "bigint"}}, "name": "transfer_list", "schema": "public", "indexes": [{"columnNames": ["gameworld_id"], "composite": false, "keyName": "transfer_list_gameworld_id_index", "constraint": false, "primary": false, "unique": false}, {"columnNames": ["auction_end_time"], "composite": false, "keyName": "transfer_list_auction_end_time_index", "constraint": false, "primary": false, "unique": false}, {"keyName": "transfer_list_player_player_id_gameworld_id_unique", "columnNames": ["player_player_id", "gameworld_id"], "composite": true, "constraint": true, "primary": false, "unique": true}, {"keyName": "transfer_list_pkey", "columnNames": ["id"], "composite": false, "constraint": true, "primary": true, "unique": true}], "checks": [], "foreignKeys": {"transfer_list_player_player_id_foreign": {"constraintName": "transfer_list_player_player_id_foreign", "columnNames": ["player_player_id"], "localTableName": "public.transfer_list", "referencedColumnNames": ["player_id"], "referencedTableName": "public.players", "updateRule": "cascade"}}, "nativeEnums": {}}, {"columns": {"id": {"name": "id", "type": "uuid", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "uuid_generate_v4()", "mappedType": "uuid"}, "transfer_listing_id": {"name": "transfer_listing_id", "type": "uuid", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "uuid"}, "team_id": {"name": "team_id", "type": "uuid", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "uuid"}, "maximum_bid": {"name": "maximum_bid", "type": "numeric(15,2)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "precision": 15, "scale": 2, "mappedType": "decimal"}, "bid_time": {"name": "bid_time", "type": "bigint", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "bigint"}, "is_winning_bid": {"name": "is_winning_bid", "type": "boolean", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "false", "mappedType": "boolean"}}, "name": "bid_history", "schema": "public", "indexes": [{"columnNames": ["transfer_listing_id"], "composite": false, "keyName": "bid_history_transfer_listing_id_index", "constraint": false, "primary": false, "unique": false}, {"columnNames": ["team_id"], "composite": false, "keyName": "bid_history_team_id_index", "constraint": false, "primary": false, "unique": false}, {"keyName": "bid_history_transfer_listing_id_team_id_unique", "columnNames": ["transfer_listing_id", "team_id"], "composite": true, "constraint": true, "primary": false, "unique": true}, {"keyName": "bid_history_pkey", "columnNames": ["id"], "composite": false, "constraint": true, "primary": true, "unique": true}], "checks": [], "foreignKeys": {"bid_history_transfer_listing_id_foreign": {"constraintName": "bid_history_transfer_listing_id_foreign", "columnNames": ["transfer_listing_id"], "localTableName": "public.bid_history", "referencedColumnNames": ["id"], "referencedTableName": "public.transfer_list", "deleteRule": "cascade"}, "bid_history_team_id_foreign": {"constraintName": "bid_history_team_id_foreign", "columnNames": ["team_id"], "localTableName": "public.bid_history", "referencedColumnNames": ["team_id"], "referencedTableName": "public.team", "updateRule": "cascade"}}, "nativeEnums": {}}, {"columns": {"id": {"name": "id", "type": "uuid", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "uuid_generate_v4()", "mappedType": "uuid"}, "date": {"name": "date", "type": "bigint", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "bigint"}, "player_id": {"name": "player_id", "type": "uuid", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "uuid"}, "buyerTeam": {"name": "buyerTeam", "type": "uuid", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "uuid"}, "sellerTeam": {"name": "sellerTeam", "type": "uuid", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "uuid"}, "value": {"name": "value", "type": "bigint", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "bigint"}, "counter_offer_time": {"name": "counter_offer_time", "type": "bigint", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "0", "mappedType": "bigint"}, "counter_offer_value": {"name": "counter_offer_value", "type": "bigint", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "0", "mappedType": "bigint"}}, "name": "transfer_request", "schema": "public", "indexes": [{"keyName": "transfer_request_player_id_buyerTeam_unique", "columnNames": ["player_id", "buyerTeam"], "composite": true, "constraint": true, "primary": false, "unique": true}, {"keyName": "transfer_request_pkey", "columnNames": ["id"], "composite": false, "constraint": true, "primary": true, "unique": true}], "checks": [], "foreignKeys": {"transfer_request_player_id_foreign": {"constraintName": "transfer_request_player_id_foreign", "columnNames": ["player_id"], "localTableName": "public.transfer_request", "referencedColumnNames": ["player_id"], "referencedTableName": "public.players", "updateRule": "cascade"}, "transfer_request_buyerTeam_foreign": {"constraintName": "transfer_request_buyerTeam_foreign", "columnNames": ["buyerTeam"], "localTableName": "public.transfer_request", "referencedColumnNames": ["team_id"], "referencedTableName": "public.team", "updateRule": "cascade"}, "transfer_request_sellerTeam_foreign": {"constraintName": "transfer_request_sellerTeam_foreign", "columnNames": ["sellerTeam"], "localTableName": "public.transfer_request", "referencedColumnNames": ["team_id"], "referencedTableName": "public.team", "updateRule": "cascade"}}, "nativeEnums": {}}], "nativeEnums": {}}