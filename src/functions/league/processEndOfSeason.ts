import { LeagueRules } from '@/entities/LeagueRules.js';
import { Team } from '@/entities/Team.js';
import { AvailableTeam } from '@/entities/AvailableTeam.js';
import { LeagueProcessor, TeamMovement } from '@/functions/league/logic/LeagueProcessorV2.js';
import {
  processPlayerAging,
  sendRetirementNotifications,
} from '@/functions/league/logic/PlayerRetirement.js';
import { sqsMiddify } from '@/middleware/sqs/index.js';
import { SQSEvent } from '@/middleware/sqs/types.js';
import { SQS } from '@/services/sqs/sqs.js';
import {
  TeamRepository,
  TransactionType,
} from '@/storage-interface/teams/team-repository.interface.js';
import { GameworldRepository } from '@/storage-interface/gameworld/gameworld-repository.interface.js';
import { GeneratePlayersEvent } from '@/types/generated/generate-players-event.js';
import { Lambda } from '@/utils/lambda.js';
import { logger } from '@/utils/logger.js';
import { tracer } from '@/utils/tracer.js';

const sqs = new SQS({ tracer });

export interface ProcessEndOfSeasonEvent {
  gameworldId: string;
}

export async function awardPrizeMoney(
  sortedTeams: Team[],
  leagueRules: LeagueRules,
  teamRepository: TeamRepository
) {
  const prizeDifference = leagueRules.maximumPrize - leagueRules.minimumPrize;
  const prizeDifferencePerPosition = prizeDifference / (leagueRules.teamCount - 1);

  const promises = sortedTeams.map((team, index) => {
    const prizeMoney =
      leagueRules.minimumPrize + (leagueRules.teamCount - 1 - index) * prizeDifferencePerPosition;
    return teamRepository.updateTeamBalance(
      team.teamId,
      team.gameworldId,
      prizeMoney,
      TransactionType.PRIZE_MONEY
    );
  });

  await Promise.all(promises);
}

async function requestYouthPlayers(sortedTeams: Team[]) {
  const payload: GeneratePlayersEvent[] = sortedTeams.map((team) => ({
    gameworldId: team.gameworldId,
    leagueId: team.league.id,
    tier: team.tier,
    teamId: team.teamId,
    managerId: team.manager?.managerId,
    requiredPlayers: 3,
    minAge: 16,
    maxAge: 18,
    minSkill: 1,
    maxSkill: 10,
    minPotential: 1,
    maxPotential: 40,
  }));

  await sqs.sendBatch(
    process.env.GENERATE_PLAYERS_QUEUE_URL!,
    payload.map((event) => ({
      Id: event.teamId,
      MessageBody: JSON.stringify(event),
    }))
  );
}

const main = async function (event: SQSEvent<ProcessEndOfSeasonEvent>): Promise<void> {
  logger.debug('Processing end of season');

  for (const record of event.Records) {
    const { gameworldId } = record.body;
    logger.debug('Processing gameworld', { gameworldId });

    // Get repositories from context (injected by middleware)
    const { leagueRepository, teamRepository } = event.context.repositories;

    // Process player aging and retirement first
    logger.debug('Processing player aging and retirement', { gameworldId });
    const { newlyRetiringPlayers, totalPlayersAged, removedPlayers } = await processPlayerAging(
      gameworldId,
      event.context.repositories
    );

    // Send retirement notifications for newly retiring players
    if (newlyRetiringPlayers.length > 0) {
      await sendRetirementNotifications(newlyRetiringPlayers, event.context.repositories);
    }

    logger.info('Player aging and retirement completed', {
      gameworldId,
      totalPlayersAged,
      newlyRetiringPlayers: newlyRetiringPlayers.length,
      removedPlayers: removedPlayers,
    });

    // Get all leagues with their teams in a single request
    const leagues = await leagueRepository.getLeaguesByGameworld(gameworldId, true);

    logger.debug('Retrieved leagues with teams', {
      leaguesCount: leagues.length,
      teamsCount: leagues.reduce((count, league) => count + (league.teams?.length || 0), 0),
    });

    if (leagues.length === 0) {
      logger.warn('No leagues found for gameworld', { gameworldId });
      return;
    }

    // Create a map of sorted teams by league
    const sortedLeagues = new Map<string, Team[]>();
    const allTeams: Team[] = [];

    // Process each league and sort its teams
    for (const league of leagues) {
      if (!league.teams || league.teams.length === 0) {
        continue;
      }

      allTeams.push(...league.teams.getItems());
      const sortedTeams = LeagueProcessor.sortTeamsInLeague(league.teams.getItems());
      sortedLeagues.set(league.id, sortedTeams);

      await awardPrizeMoney(sortedTeams, league.leagueRules, teamRepository);
      await requestYouthPlayers(sortedTeams);

      logger.debug('Sorted teams in league', {
        leagueId: league.id,
        teamCount: sortedTeams.length,
        standings: sortedTeams.map(LeagueProcessor.getTeamStandings),
      });
    }

    if (allTeams.length === 0) {
      logger.warn('No teams found for gameworld', { gameworldId });
      return;
    }

    const movements = LeagueProcessor.processPromotionsAndRelegations(sortedLeagues, leagues);

    await teamRepository.updateTeamLeagues(allTeams, movements, gameworldId);

    await teamRepository.flush();

    logger.info('Processed end of season movements and reset standings', {
      totalMovements: movements.length,
      movements: movements.map((m) => ({
        teamId: m.teamId,
        from: m.fromLeagueId,
        to: m.toLeagueId,
      })),
    });

    // generate new fixtures
    // Create Lambda client
    const lambdaClient = new Lambda({ tracer });

    // Invoke fixtures lambda directly
    const generateFixturesLambdaArn = process.env.GENERATE_FIXTURES_LAMBDA_ARN!;

    for (const league of leagues) {
      await lambdaClient.eventInvoke(
        JSON.stringify({
          gameworldId: league.gameworld.id,
          leagueId: league.id,
        }),
        generateFixturesLambdaArn
      );
    }
  }
};

export const handler = sqsMiddify<ProcessEndOfSeasonEvent>(main, {});
