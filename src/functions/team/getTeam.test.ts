import { beforeEach, describe, expect, it, vi } from 'vitest';

import { buildResponse } from '@/utils/buildResponse.js';

// Import the main function directly
import { Player } from '@/entities/Player.js';
import { Team } from '@/entities/Team.js';
import createHttpEvent from '@/testing/createHttpEvent.js';
import { PlayerFactory } from '@/testing/factories/playerFactory.js';
import { TeamsFactory } from '@/testing/factories/teamFactory.js';
import { handler } from './getTeam.js';

// Mock the repositories
const mockTeamRepository = {
  getTeam: vi.fn(),
  getTeamAndNextMatch: vi.fn(),
};

// Mock the getUser function
vi.mock('@/utils/getUser.js', () => ({
  getUser: vi.fn().mockReturnValue('defaultUserId'),
}));

// Mock the httpMiddify function to return the main function directly
vi.mock('@/middleware/rest/index.js', () => ({
  httpMiddify: vi.fn((fn) => fn),
}));

describe('getTeam', () => {
  const mockContext = {} as any; // Mock context object
  const mockTeam: Team = TeamsFactory.build();

  const mockPlayers: Player[] = [
    PlayerFactory.build({
      playerId: 'player1',
      team: TeamsFactory.build({ teamId: 'team1' }),
      gameworldId: 'gameworld1',
      firstName: 'John',
      surname: 'Doe',
      age: 25,
      value: 1000000,
      seed: 123,
    }),
    PlayerFactory.build({
      playerId: 'player2',
      team: TeamsFactory.build({ teamId: 'team1' }),
      gameworldId: 'gameworld1',
      firstName: 'Jane',
      surname: 'Smith',
      age: 23,
      value: 1200000,
      seed: 456,
    }),
    PlayerFactory.build({
      playerId: 'player3',
      team: TeamsFactory.build({ teamId: 'team1' }),
      gameworldId: 'gameworld1',
      firstName: 'Bob',
      surname: 'Wilson',
      age: 28,
      value: 800000,
      seed: 789,
    }),
  ];

  beforeEach(() => {
    vi.clearAllMocks();
    mockTeamRepository.getTeam.mockReset();
    mockTeamRepository.getTeamAndNextMatch.mockReset();
  });

  it('should return 404 if team is not found', async () => {
    mockTeamRepository.getTeamAndNextMatch.mockResolvedValue({ team: null, nextFixture: null });

    const event = createHttpEvent({
      pathParameters: {
        teamId: 'team1',
        gameworldId: 'gameworld1',
      },
    });

    // Add repository context that middleware would normally provide
    event.context = {
      repositories: {
        teamRepository: mockTeamRepository,
      },
    };

    const response = await handler(event, mockContext);

    expect(mockTeamRepository.getTeamAndNextMatch).toHaveBeenCalledWith(
      'gameworld1',
      'team1',
      false
    );
    expect(response).toEqual(buildResponse(404, JSON.stringify({ error: 'Team not found' })));
  });

  it('should return team without players when includePlayers is false', async () => {
    mockTeamRepository.getTeamAndNextMatch.mockResolvedValue({ team: mockTeam, nextFixture: null });

    const event = createHttpEvent({
      pathParameters: {
        teamId: 'team1',
        gameworldId: 'gameworld1',
      },
    });

    // Add repository context that middleware would normally provide
    event.context = {
      repositories: {
        teamRepository: mockTeamRepository,
      },
    };

    const response = await handler(event, mockContext);

    expect(mockTeamRepository.getTeamAndNextMatch).toHaveBeenCalledWith(
      'gameworld1',
      'team1',
      false
    );
    expect(response).toEqual(buildResponse(200, JSON.stringify({ ...mockTeam, players: [] })));
  });

  it('should return team with ordered players when includePlayers is true', async () => {
    // Create a team with players already included (as the repository would return)
    const teamWithPlayers = {
      ...mockTeam,
      players: mockPlayers,
    };

    mockTeamRepository.getTeamAndNextMatch.mockResolvedValue({
      team: teamWithPlayers,
      nextFixture: null,
    });

    const event = createHttpEvent({
      pathParameters: {
        teamId: 'team1',
        gameworldId: 'gameworld1',
      },
      queryStringParameters: {
        includePlayers: 'true',
      },
    });

    // Add repository context that middleware would normally provide
    event.context = {
      repositories: {
        teamRepository: mockTeamRepository,
      },
    };

    const response = await handler(event, mockContext);
    const responseBody = JSON.parse(response.body);

    expect(mockTeamRepository.getTeamAndNextMatch).toHaveBeenCalledWith(
      'gameworld1',
      'team1',
      'true'
    );

    // Verify players are ordered according to selectionOrder
    expect(responseBody.players[0].playerId).toBe('player1');
    expect(responseBody.players[1].playerId).toBe('player2');
    expect(responseBody.players[2].playerId).toBe('player3');

    // Should return trainingPotential between 0 and 1
    responseBody.players.forEach((player: any) => {
      expect(player.trainingPotential).toBeDefined();
      expect(player.trainingPotential).toBeGreaterThanOrEqual(0);
      expect(player.trainingPotential).toBeLessThanOrEqual(1);
    });

    // Should return topTrainingAreas as an array of 3 strings
    // Strings should be the attribute names without Current or Potential suffix
    responseBody.players.forEach((player: any) => {
      expect(player.topTrainingAreas).toBeDefined();
      expect(Array.isArray(player.topTrainingAreas)).toBe(true);
      expect(player.topTrainingAreas.length).toBe(3);
      player.topTrainingAreas.forEach((area: string) => {
        expect(typeof area).toBe('string');
        expect(area).not.toContain('Current');
        expect(area).not.toContain('Potential');
      });
    });
  });
});
