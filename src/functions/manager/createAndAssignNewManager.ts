import { AvailableTeam } from '@/entities/AvailableTeam.js';
import { Manager } from '@/entities/Manager.js';
import { sqsMiddify } from '@/middleware/sqs/index.js';
import { SQSEvent } from '@/middleware/sqs/types.js';
import { ManagerRepository } from '@/storage-interface/managers/manager-repository.interface.js';
import { TeamRepository } from '@/storage-interface/teams/team-repository.interface.js';
import { NewManagerEvent } from '@/types/generated/new-manager-event.js';
import { logger } from '@/utils/logger.js';

export async function getAndDelete(teamRepository: TeamRepository): Promise<AvailableTeam | null> {
  const team = await teamRepository.getRandomAvailableTeam();
  if (!team) {
    return null;
  }

  await teamRepository.deleteAvailableTeam(team);
  return team;
}

export async function createAndAssignNewManager(
  managerId: string,
  teamRepository: TeamRepository,
  managerRepository: ManagerRepository,
  email?: string
) {
  const existingManager = await managerRepository.getManagerById(managerId);
  if (existingManager) {
    logger.error('Manager already exists', { existingManager });
    return;
  }

  try {
    // Get a team from the available teams table
    logger.debug('Getting team');
    const team = await getAndDelete(teamRepository);
    if (!team) {
      throw new Error('No available teams');
    }
    logger.debug('Got team', { team });
    const teamId = team.teamId;
    const gameworldId = team.gameworldId;

    // Create a new manager
    const manager = new Manager();
    manager.managerId = managerId;
    manager.team = teamRepository.createFromPK(teamId);
    manager.email = email;
    manager.gameworldId = gameworldId;
    manager.scoutTokens = 3;
    manager.superScoutTokens = 0;
    manager.createdAt = new Date().getTime();
    manager.lastActive = new Date().getTime();

    // Save the manager
    logger.debug('Saving manager', { manager });
    await managerRepository.createManager(manager);

    logger.debug('Manager saved');
  } catch (error) {
    logger.error('Error saving user:', { error });
    throw error;
  }
}

async function main(event: SQSEvent<NewManagerEvent>) {
  // Get repositories from context (injected by middleware)
  const { teamRepository, managerRepository } = event.context.repositories;

  const promises = [];
  for (const record of event.Records) {
    const { managerId, email } = record.body;
    if (!managerId) {
      throw new Error('Manager ID is required');
    }
    // Process each manager creation
    promises.push(createAndAssignNewManager(managerId, teamRepository, managerRepository, email));
  }
  await Promise.all(promises);
}

export const handler = sqsMiddify<NewManagerEvent>(main);
