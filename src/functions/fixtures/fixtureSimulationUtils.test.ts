import { Fixture } from '@/entities/Fixture.js';
import { FixtureDetails } from '@/model/fixture.ts';
import { SQS } from '@/services/sqs/sqs.js';
import { LeagueFactory } from '@/testing/factories/leagueFactory.ts';
import { PlayerFactory } from '@/testing/factories/playerFactory.js';
import { TeamsFactory } from '@/testing/factories/teamFactory.js';
import { SendMessageCommandInput, SendMessageCommandOutput } from '@aws-sdk/client-sqs';
import { afterEach, beforeEach, describe, expect, it, MockInstance, vi } from 'vitest';
import {
  debugProcessNextFixtures,
  sendFixturesToQueue,
  updatePlayerEnergy,
} from './fixtureSimulationUtils.js';

describe('updatePlayerEnergy', () => {
  const lastMatchPlayed = Date.now() - 1000 * 60 * 60 * 14; // 14 hours ago
  const mockPlayers = PlayerFactory.batch(5, { energy: 50, lastMatchPlayed: lastMatchPlayed });
  it('should update player energy correctly', () => {
    const energiesBefore = mockPlayers.map((player) => player.energy);
    updatePlayerEnergy(mockPlayers);
    const energiesAfter = mockPlayers.map((player) => player.energy);
    for (let i = 0; i < energiesBefore.length; i++) {
      expect(energiesAfter[i]).toBeGreaterThanOrEqual(energiesBefore[i] ?? 0);
    }
  });
});

describe('sendFixturesToQueue', () => {
  let sendSpy: MockInstance<
    (
      queueUrl: string,
      payload: string,
      additionalOpts?: Partial<SendMessageCommandInput>
    ) => Promise<SendMessageCommandOutput>
  >;

  beforeEach(() => {
    vi.clearAllMocks();
    process.env.QUEUE_URL = 'https://sqs.queue.url';
    sendSpy = vi.spyOn(SQS.prototype, 'send').mockResolvedValue({} as any);
  });

  const fixtures: FixtureDetails[] = [
    {
      fixtureId: 'fixture1',
      gameworldId: 'gameworld1',
      leagueId: 'league1',
      homeTeamId: 'homeTeam1',
      homeTeamName: 'Home Team 1',
      awayTeamId: 'awayTeam1',
      awayTeamName: 'Away Team 1',
      date: 1741165200000,
      stats: {} as any,
      events: [],
    },
  ];

  it('sends valid fixtures to SQS', async () => {
    await sendFixturesToQueue(fixtures);
    expect(sendSpy).toHaveBeenCalledTimes(1);
    expect(sendSpy).toHaveBeenCalledWith(
      process.env.QUEUE_URL,
      expect.stringContaining('"fixtureId":"fixture1"')
    );
  });

  it('throws an error if QUEUE_URL is not set', async () => {
    delete process.env.QUEUE_URL;
    await expect(sendFixturesToQueue(fixtures)).rejects.toThrow(
      'QUEUE_URL environment variable not set'
    );
  });

  it('throws an error for invalid fixture payload', async () => {
    const invalidFixtures = [
      {
        ...fixtures[0],
        homeTeamId: null, // Invalid team data - null values
        homeTeamName: null,
      },
    ] as any;
    await expect(sendFixturesToQueue(invalidFixtures)).rejects.toThrow(
      'Invalid fixture payload for SQS'
    );
  });
});

describe('debugProcessNextFixtures', () => {
  const mockFixtureRepository = {
    getDueFixtures: vi.fn(),
    getAllUnplayedFixtures: vi.fn(),
  } as any;

  const fixtures: Fixture[] = [
    {
      fixtureId: 'fixture1',
      gameworldId: 'gameworld1',
      league: LeagueFactory.build(),
      homeTeam: TeamsFactory.build(),
      awayTeam: TeamsFactory.build(),
      date: 1741165200000,
      played: false,
    },
    {
      fixtureId: 'fixture2',
      gameworldId: 'gameworld1',
      league: LeagueFactory.build(),
      homeTeam: TeamsFactory.build(),
      awayTeam: TeamsFactory.build(),
      date: 1741165200000,
      played: false,
    },
  ];

  beforeEach(() => {
    vi.useFakeTimers();
    vi.setSystemTime(new Date(1741165200000));
  });
  afterEach(() => {
    vi.useRealTimers();
  });

  it('returns unplayed fixtures sorted by date', async () => {
    mockFixtureRepository.getAllUnplayedFixtures.mockResolvedValue(fixtures);

    const result = await debugProcessNextFixtures(mockFixtureRepository, 'gameworld1', 'league1');
    expect(result).toEqual([
      {
        awayTeamId: fixtures[0]!.awayTeam.teamId,
        awayTeamName: fixtures[0]!.awayTeam.teamName,
        date: 1741165200000,
        events: [],
        fixtureId: 'fixture1',
        gameworldId: 'gameworld1',
        homeTeamId: fixtures[0]!.homeTeam.teamId,
        homeTeamName: fixtures[0]!.homeTeam.teamName,
        leagueId: fixtures[0]!.league.id,
        stats: {},
      },
      {
        awayTeamId: fixtures[1]!.awayTeam.teamId,
        awayTeamName: fixtures[1]!.awayTeam.teamName,
        date: 1741165200000,
        events: [],
        fixtureId: 'fixture2',
        gameworldId: 'gameworld1',
        homeTeamId: fixtures[1]!.homeTeam.teamId,
        homeTeamName: fixtures[1]!.homeTeam.teamName,
        leagueId: fixtures[1]!.league.id,
        stats: {},
      },
    ]);
  });

  it('returns an empty array if no unplayed fixtures are found', async () => {
    mockFixtureRepository.getAllUnplayedFixtures.mockResolvedValue([]);
    const result = await debugProcessNextFixtures(mockFixtureRepository, 'gameworld1', 'league1');
    expect(result).toEqual([]);
  });
});
