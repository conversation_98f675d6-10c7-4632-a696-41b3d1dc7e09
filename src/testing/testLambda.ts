/* eslint-disable @typescript-eslint/no-unsafe-argument,@typescript-eslint/no-explicit-any */
import { handler } from '@/functions/player/getTransferListPlayers.js';
import { initializeDatabase } from '@/storage-interface/database-initializer.js';
import createHttpEvent from '@/testing/createHttpEvent.js';
import { logger } from '@/utils/logger.js';

process.env.DATABASE_TYPE = 'postgres';
process.env.DEBUG_USER_ID = 'a14be5a0-c011-70a1-9bdb-d21eaed0a96b';

async function startTest() {
  await initializeDatabase();

  const mockEvent = createHttpEvent({
    queryStringParameters: {
      sortBy: 'auctionCurrentPrice',
      sortDirection: 'ASC',
    },
    pathParameters: {
      gameworldId: '7df5a880-2b6e-4e1e-8cc5-736bb85ae8a4',
      teamId: 'a0756689-3d3f-46eb-9e22-a2c9e33dbf4f',
    },
    httpMethod: 'GET',
  });
  /*const mockEvent = createSqsEvent([
    {
      messageId: '8041523a-6fc1-4877-af79-0090f5723b5d',
      receiptHandle:
        'AQEBLsp9K9whsa0vV7dkN44mfUCkNey54mkVB3hwqJMlXnjolJOXieuGFLV5qRLzHvtWxL4Q2kVsXmgc/4tLObF7Y/PIIVQose3vq9ORGLIo/PK809yvehkc6WP4svlQudiD/0MLUmLmomK77Mrx0ryHXbe0K5MxHfLRpFiOcodkInLU5IU3u7+/tk+ZTgGJSAMXcz7owYf8UbyVpjVzXAr/wmLdIPGSdFpQJ/Vv6wj/Ois+kQ6HhIb0CHXP/g5dlWq67Gc/C3k5P4upWJCKb/9v/tAh8XOSeBhFEWRAHDD1D9cgVqJZcLy0PePe/7GHS8Iq/KJxk7qeAaHhXCeYJaBR1cjVXKlGfzbzUajWxGyruKCpgnLL14dlJt5zrc3arJF28/n71f0cCUHfqFiG4lvwABG82f775F0fqQYn6+ZmI/4=',
      body: {
        gameworldId: '4c3846b1-318e-413d-970a-c32d9d3b9ef6',
        leagueId: 'b09a244b-cff4-488a-8ee3-a1b6286c8416',
        fixtureId: '2a43c238-249e-4fc2-81fe-9daca64a3314',
        homeTeamId: 'ad7c5af3-2b49-4f00-950f-af54f6fa6f00',
        homeTeamName: 'Drayfell FC',
        awayTeamId: 'bb2daaf8-a69d-47b2-8ad1-81e5175fe78e',
        awayTeamName: 'Dunsmere FC',
        date: 1749891600000,
      },
      attributes: {
        ApproximateReceiveCount: '1',
        SentTimestamp: '1749835883801',
        SenderId: '899341869220',
        ApproximateFirstReceiveTimestamp: '1749835887927',
      },
      messageAttributes: {},
      md5OfBody: '03968736a38e0ea51e2f8a652915716c',
      eventSource: 'aws:sqs',
      eventSourceARN: 'arn:aws:sqs:us-east-2:899341869220:stage-fixtureQueue-2cda1ba',
      awsRegion: 'us-east-2',
    },
  ]) as any;*/

  const response = await handler(mockEvent, {} as any);
  logger.debug('response', { response });
}

startTest();
