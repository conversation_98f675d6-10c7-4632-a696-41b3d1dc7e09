import { League } from '@/entities/League.js';
import { Player } from '@/entities/Player.js';
import { PlayerMatchHistory } from '@/entities/PlayerMatchHistory.ts';
import { Collection, Ref, Rel } from '@mikro-orm/core';
import { Factory } from 'interface-forge';

export const PlayerFactory = new Factory<Player>((factory: Factory<Player>, iteration: number) => {
  const reflexesCurrent = factory.number.int({ min: 1, max: 40 });
  const positioningCurrent = factory.number.int({ min: 1, max: 40 });
  const shotStoppingCurrent = factory.number.int({ min: 1, max: 40 });
  const tacklingCurrent = factory.number.int({ min: 1, max: 40 });
  const markingCurrent = factory.number.int({ min: 1, max: 40 });
  const headingCurrent = factory.number.int({ min: 1, max: 40 });
  const finishingCurrent = factory.number.int({ min: 1, max: 40 });
  const paceCurrent = factory.number.int({ min: 1, max: 40 });
  const crossingCurrent = factory.number.int({ min: 1, max: 40 });
  const passingCurrent = factory.number.int({ min: 1, max: 40 });
  const visionCurrent = factory.number.int({ min: 1, max: 40 });
  const ballControlCurrent = factory.number.int({ min: 1, max: 40 });

  return {
    playerId: factory.string.uuid(),
    gameworldId: factory.string.uuid(),
    teamId: factory.string.uuid(),
    leagueId: {
      id: factory.string.uuid(),
    } as Ref<League>,
    firstName: factory.person.firstName(),
    surname: factory.person.lastName(),
    age: factory.number.int({ min: 17, max: 39 }),
    seed: factory.number.int({ min: 0, max: 1000000 }),
    value: factory.number.int({ min: 100000, max: 10000000 }),
    energy: factory.number.int({ min: 0, max: 100 }),
    lastMatchPlayed: factory.date.recent({ days: 1 }).getTime(),
    suspendedForGames: factory.number.int({ min: 0, max: 2 }),
    injuredUntil: factory.date.soon({ days: 1 }).getTime(),
    isTransferListed: factory.datatype.boolean(),
    attributes: {
      player: {} as Rel<Player>,
      reflexesCurrent,
      reflexesPotential: factory.number.int({ min: reflexesCurrent, max: 40 }),
      positioningCurrent,
      positioningPotential: factory.number.int({ min: positioningCurrent, max: 40 }),
      shotStoppingCurrent,
      shotStoppingPotential: factory.number.int({ min: shotStoppingCurrent, max: 40 }),
      tacklingCurrent,
      tacklingPotential: factory.number.int({ min: tacklingCurrent, max: 40 }),
      markingCurrent,
      markingPotential: factory.number.int({ min: markingCurrent, max: 40 }),
      headingCurrent,
      headingPotential: factory.number.int({ min: headingCurrent, max: 40 }),
      finishingCurrent,
      finishingPotential: factory.number.int({ min: finishingCurrent, max: 40 }),
      paceCurrent,
      pacePotential: factory.number.int({ min: paceCurrent, max: 40 }),
      crossingCurrent,
      crossingPotential: factory.number.int({ min: crossingCurrent, max: 40 }),
      passingCurrent,
      passingPotential: factory.number.int({ min: passingCurrent, max: 40 }),
      visionCurrent,
      visionPotential: factory.number.int({ min: visionCurrent, max: 40 }),
      ballControlCurrent,
      ballControlPotential: factory.number.int({ min: ballControlCurrent, max: 40 }),
      stamina: factory.number.float({ min: 0.1, max: 1.0 }),
      isGoalkeeper: factory.datatype.boolean(),
    },
    matchHistory: new Collection<PlayerMatchHistory>({}),
    retiringAtEndOfSeason: factory.datatype.boolean(),
  };
});
