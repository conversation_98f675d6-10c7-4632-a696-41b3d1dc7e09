import { Player } from '@/entities/Player.js';
import { Team } from '@/entities/Team.js';
import { CurrentAttributes } from '@/model/player.js';

export interface LeagueStandings {
  played: number;
  points: number;
  goalsFor: number;
  goalsAgainst: number;
  wins: number;
  draws: number;
  losses: number;
}

// Interface for player response with current attributes only
export type PlayerResponse = Omit<Player, 'attributes' | 'team'> & {
  attributes: CurrentAttributes;
  teamId: string;
  trainingPotential: number; // 0-1 how much potential the player has left to train
  topTrainingAreas: string[]; // the top 3 training areas for the player
};

export type GetTeamResponse = Omit<Team, 'players'> & {
  players: PlayerResponse[];
  nextFixture?: {
    fixtureId: string;
    date: number;
  };
};
