config:
  jfg-backend:logLevel: DEBUG
  jfg-backend:googleClientId: 895242317375-tb01eubvnh50vt1osn4enagk5j19e1q3.apps.googleusercontent.com
  jfg-backend:googleClientSecret:
    secure: AAABAGWMRXpw8nM4sC8xJLosmSN5nFaplndqJqfPrahirm0mBE3Yq20lTDnVSqPvvLjXszNuej34JKg2URk30ZL8Jg==
  jfg-backend:alarmEmail: <EMAIL>
  jfg-backend:databaseType: postgres
  jfg-backend:postgresUrl: aws-0-us-east-2.pooler.supabase.com
  jfg-backend:postgresUser: postgres.firmrxnzhlkubwtpbsny
  jfg-backend:postgresPassword:
    secure: AAABAB4RGf0QZ51ZF7tacTLlR/crvLu299051iUHTdSH+o5+kVClF7F2BBDHLg==
  jfg-backend:mjmlAppId:
    secure: AAABAG8M1rWEQCZNY2ssoVVPZ9SJvlcQLp0lKhjfpbGoNP+Z85Kk+x5k8Fq2B9Hu8xs5uyLKILck54wWoB2UxCc/wZ0=
  jfg-backend:mjmlSecret:
    secure: AAABAG523xLLku7ptTIsmwlpxLaVIp/qsJQZO0FJypO2CbBk8csNfo2njvLr4s72lCAYZWYmvdXNIDnXqT+9O0vSAwI=
  jfg-backend:postgresPort: "6543"
