import { Table } from '@pulumi/aws/dynamodb';
import { Function } from '@pulumi/aws/lambda';
import { sqsBatchWindow } from '../config';
import { addDynamoPolicyToRole } from '../dynamodb';
import { createScheduledRule } from '../eventBridge';
import { addLambdaInvokePolicyToRole, createLambdaFunction } from '../lambda';
import {
  addLambdaPermissionForSQS,
  addQueueReadPolicyToRole,
  addQueueSendPolicyToRole,
  createDLQ,
  createQueue,
} from '../queue';
import { createCloudWatchAlarmTopic, createMonitoredEventSourceMapping } from '../queueMonitoring';

export function createFixtureResources({
  fixtureDetailTable,
  getTeamLambda,
}: {
  fixtureDetailTable: Table;
  getTeamLambda: Function;
}) {
  let getFixtureRole = addDynamoPolicyToRole(
    'getFixtureHandler',
    [fixtureDetailTable],
    ['dynamodb:GetItem', 'dynamodb:BatchGetItem', 'dynamodb:BatchWriteItem']
  );

  const [getFixtureLambda] = createLambdaFunction(
    'getFixtureHandler',
    '../dist/fixtures/getFixture',
    'index.handler',
    { FIXTURE_DETAIL_TABLE_NAME: fixtureDetailTable.name },
    getFixtureRole
  );

  /*const [migrateFixtureDetailsLambda] = createLambdaFunction(
    'migrateFixtureDetails',
    '../dist/fixtures/debugMigrateFixtureDetails',
    'index.handler',
    { FIXTURE_DETAIL_TABLE_NAME: fixtureDetailTable.name },
    getFixtureRole,
    undefined,
    {
      memorySize: 512,
      timeout: 300,
    }
  );*/

  // Fixture processing
  const fixtureDLQ = createDLQ('fixtureQueue');
  const fixtureQueue = createQueue('fixtureQueue', fixtureDLQ, 1);
  const fixtureQueueRole = addQueueReadPolicyToRole('fixtureQueue', fixtureQueue);
  let fixtureRole = addDynamoPolicyToRole(
    'processFixtures',
    [fixtureDetailTable],
    ['dynamodb:PutItem', 'dynamodb:UpdateItem', 'dynamodb:BatchWriteItem'],
    fixtureQueueRole
  );
  fixtureRole = addLambdaInvokePolicyToRole('processFixtures', [getTeamLambda], fixtureRole);
  const [fixtureLambda, fixtureLogGroup] = createLambdaFunction(
    'processFixtures',
    '../dist/fixtures/simulateFixtures',
    'index.handler',
    {
      QUEUE_URL: fixtureQueue.url,
      FIXTURE_DETAIL_TABLE_NAME: fixtureDetailTable.name,
      GET_TEAM_LAMBDA_ARN: getTeamLambda.arn,
    },
    fixtureRole,
    [
      {
        source: '../src/simulation/commentary.csv',
        destination: 'commentary.csv',
      },
    ],
    {
      memorySize: 384,
      timeout: 60,
    }
  );
  // Add Lambda Permission for SQS to invoke it
  addLambdaPermissionForSQS('processFixtures', fixtureLambda, fixtureQueue);

  // Create an SNS topic for CloudWatch alarms
  const errorAlarmTopic = createCloudWatchAlarmTopic('fixture-error');

  // Add SQS Queue Event Source Mapping to Lambda with monitoring
  createMonitoredEventSourceMapping(
    'processFixtures',
    fixtureLambda,
    fixtureQueue,
    fixtureDLQ,
    10, // batchSize
    sqsBatchWindow, // maximumBatchingWindowInSeconds
    {
      functionResponseTypes: ['ReportBatchItemFailures'],
    }, // additionalConfig
    [errorAlarmTopic.arn] // alarmActions
  );

  const [getCommentaryLambda] = createLambdaFunction(
    'getCommentaryHandler',
    '../dist/fixtures/getLocalisation',
    'index.handler',
    undefined,
    undefined,
    [
      {
        source: '../src/simulation/commentary.csv',
        destination: 'commentary.csv',
      },
    ]
  );

  const [getFixturesLambda] = createLambdaFunction(
    'getFixturesHandler',
    '../dist/fixtures/getLeagueFixtures',
    'index.handler'
  );
  const [getTeamFixturesLambda] = createLambdaFunction(
    'getTeamFixturesHandler',
    '../dist/fixtures/getTeamFixtures',
    'index.handler',
    undefined,
    undefined,
    undefined,
    {
      memorySize: 256,
      timeout: 60,
    }
  );

  // Create the scheduled fixture simulation lambda
  let scheduleFixtureSimulationRole = addQueueSendPolicyToRole(
    'scheduleFixtureSimulation',
    fixtureQueue
  );

  const [scheduleFixtureSimulationLambda, scheduleFixtureSimulationLogGroup] = createLambdaFunction(
    'scheduleFixtureSimulation',
    '../dist/fixtures/scheduleFixtureSimulation',
    'index.handler',
    {
      QUEUE_URL: fixtureQueue.url,
    },
    scheduleFixtureSimulationRole,
    undefined,
    {
      memorySize: 256,
      timeout: 30,
    }
  );

  // Create EventBridge rules to trigger the lambda at 10am and 8pm UK time
  createScheduledRule({
    name: 'fixture-simulation-10am',
    description: 'Trigger fixture simulation at 10am UK time every day',
    scheduleExpression: 'cron(0 9 * * ? *)', // 9am UTC = 10am UK (GMT+1)
    lambda: scheduleFixtureSimulationLambda,
  });

  createScheduledRule({
    name: 'fixture-simulation-8pm',
    description: 'Trigger fixture simulation at 8pm UK time every day',
    scheduleExpression: 'cron(0 19 * * ? *)', // 7pm UTC = 8pm UK (GMT+1)
    lambda: scheduleFixtureSimulationLambda,
  });

  // Note: DLQ monitoring is now handled by createMonitoredEventSourceMapping
  // Individual error log alarms have been removed to reduce CloudWatch costs

  return {
    fixtureLambda,
    getFixturesLambda,
    getTeamFixturesLambda,
    getFixtureLambda,
    fixtureQueue,
    getCommentaryLambda,
    scheduleFixtureSimulationLambda,
    errorAlarmTopic,
  };
}
