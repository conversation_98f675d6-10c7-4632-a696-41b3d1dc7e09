import { sqsBatchWindowMaximum, sqsBatchWindowMinimum } from '../config';
import { createScheduledRule } from '../eventBridge';
import { addLambdaInvokePolicyToRole, createLambdaFunction } from '../lambda';
import {
  addLambdaPermissionForSQS,
  addQueueReadPolicyToRole,
  addQueueSendPolicyToRole,
  createDLQ,
  createQueue,
} from '../queue';
import { createCloudWatchAlarmTopic, createMonitoredEventSourceMapping } from '../queueMonitoring';

export interface GameworldResourcesConfig {}

export function createGameworldResources(_: GameworldResourcesConfig) {
  // Create unattached players queue for direct access
  const unattachedPlayersDLQ = createDLQ('gameworldUnattachedPlayersQueue');
  const unattachedPlayersQueue = createQueue(
    'gameworldUnattachedPlayersQueue',
    unattachedPlayersDLQ
  );

  const generatePlayersDLQ = createDLQ('generatePlayersQueue');
  const generatePlayersQueue = createQueue('generatePlayersQueue', generatePlayersDLQ);

  // Create team queue for direct access
  const teamDLQ = createDLQ('gameworldTeamQueue');
  const teamQueue = createQueue('gameworldTeamQueue', teamDLQ);

  // Add send permissions to the gameworld role for both queues
  let gameworldRole = addQueueSendPolicyToRole(
    'gameworldToUnattachedPlayersQueue',
    unattachedPlayersQueue
  );
  gameworldRole = addQueueSendPolicyToRole('gameworldToTeamQueue', teamQueue, gameworldRole);

  const [gameworldLambda] = createLambdaFunction(
    'gameworldHandler',
    '../dist/generate/gameworld',
    'index.handler',
    {
      UNATTACHED_PLAYERS_QUEUE_URL: unattachedPlayersQueue.url,
      TEAM_QUEUE_URL: teamQueue.url,
    },
    gameworldRole,
    undefined,
    {
      memorySize: 512,
      timeout: 60,
    }
  );

  const [generateFixturesLambda] = createLambdaFunction(
    'generateFixtures',
    '../dist/generate/generateFixtures',
    'index.handler',
    undefined,
    undefined,
    undefined,
    {
      memorySize: 512,
      timeout: 60,
    }
  );

  // End of season processing
  const endOfSeasonDLQ = createDLQ('endOfSeasonQueue');
  const endOfSeasonQueue = createQueue('endOfSeasonQueue', endOfSeasonDLQ, 1);

  let endOfSeasonRole = addQueueReadPolicyToRole('endOfSeasonQueue', endOfSeasonQueue);

  endOfSeasonRole = addLambdaInvokePolicyToRole(
    'processEndOfSeasonInvokeGenerateFixtures',
    [generateFixturesLambda],
    endOfSeasonRole
  );

  endOfSeasonRole = addQueueSendPolicyToRole(
    'processEndOfSeasonSendToGeneratePlayersQueue',
    generatePlayersQueue,
    endOfSeasonRole
  );

  const [endOfSeasonLambda, endOfSeasonLogGroup] = createLambdaFunction(
    'processEndOfSeason',
    '../dist/league/processEndOfSeason',
    'index.handler',
    {
      QUEUE_URL: endOfSeasonQueue.url,
      GENERATE_FIXTURES_LAMBDA_ARN: generateFixturesLambda.arn,
      GENERATE_PLAYERS_QUEUE_URL: generatePlayersQueue.url,
    },
    endOfSeasonRole,
    undefined,
    {
      memorySize: 512,
      timeout: 120,
    }
  );

  // Add Lambda Permission for SQS to invoke it
  addLambdaPermissionForSQS('processEndOfSeason', endOfSeasonLambda, endOfSeasonQueue);

  // Create an SNS topic for CloudWatch alarms
  const errorAlarmTopic = createCloudWatchAlarmTopic('gameworld-error');

  // Add SQS Queue Event Source Mapping to Lambda with monitoring
  createMonitoredEventSourceMapping(
    'processEndOfSeason',
    endOfSeasonLambda,
    endOfSeasonQueue,
    endOfSeasonDLQ,
    10, // batchSize
    sqsBatchWindowMaximum, // maximumBatchingWindowInSeconds
    undefined, // additionalConfig
    [errorAlarmTopic.arn] // alarmActions
  );

  const checkRole = addQueueSendPolicyToRole('checkForSeasonEnd', endOfSeasonQueue);
  const [checkForSeasonEnd] = createLambdaFunction(
    'checkForSeasonEnd',
    '../dist/gameworld/checkForSeasonEnd',
    'index.handler',
    {
      QUEUE_URL: endOfSeasonQueue.url,
    },
    checkRole,
    undefined,
    {
      memorySize: 256,
      timeout: 30,
    }
  );

  // Create EventBridge rules to trigger the lambda at midnight UK time
  createScheduledRule({
    name: 'check-season-end',
    description: 'Triggers a lambda to see if any gameworlds season has ended',
    scheduleExpression: 'cron(0 0 * * ? *)', // Midnight daily
    lambda: checkForSeasonEnd,
  });

  const generatePlayersQueueRole = addQueueReadPolicyToRole(
    'generatePlayersQueue',
    generatePlayersQueue
  );

  const [generatePlayersLambda, generatePlayersLambdaLogGroup] = createLambdaFunction(
    'generatePlayersHandler',
    '../dist/generate/generate-players',
    'index.handler',
    {},
    generatePlayersQueueRole
  );

  // Create monitored event source mapping to connect the Lambda to the queue
  createMonitoredEventSourceMapping(
    'generatePlayers',
    generatePlayersLambda,
    generatePlayersQueue,
    generatePlayersDLQ,
    10, // batchSize
    sqsBatchWindowMinimum, // maximumBatchingWindowInSeconds
    undefined, // additionalConfig
    [errorAlarmTopic.arn] // alarmActions
  );

  const [countAvailableTeamsLambda] = createLambdaFunction(
    'countAvailableTeamsHandler',
    '../dist/gameworld/countAvailableTeams',
    'index.handler',
    undefined,
    undefined,
    undefined,
    {
      memorySize: 256,
    }
  );

  return {
    gameworldLambda,
    endOfSeasonLambda,
    endOfSeasonQueue,
    teamQueue,
    teamDLQ,
    unattachedPlayersQueue,
    unattachedPlayersDLQ,
    errorAlarmTopic,
    generateFixturesLambda,
    generatePlayersQueue,
    countAvailableTeamsLambda,
  };
}
