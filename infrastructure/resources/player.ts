import { Queue } from '@pulumi/aws/sqs';
import { sqsBatchWindowMaximum, sqsBatchWindowMinimum } from '../config';
import { createLambdaFunction } from '../lambda';
import { addLambdaPermissionForSQS, addQueueReadPolicyToRole } from '../queue';
import { createCloudWatchAlarmTopic, createMonitoredEventSourceMapping } from '../queueMonitoring';

export interface PlayerResourcesConfig {
  playerQueue: Queue;
  playerDLQ: Queue;
  unattachedPlayersQueue: Queue;
  unattachedPlayersDLQ: Queue;
}

export function createPlayerResources(config: PlayerResourcesConfig) {
  let playerQueueRole = addQueueReadPolicyToRole('playerQueue', config.playerQueue);

  const [playerLambda, playerLogGroup] = createLambdaFunction(
    'playerHandler',
    '../dist/generate/player',
    'index.handler',
    {
      QUEUE_URL: config.playerQueue.url,
    },
    playerQueueRole,
    undefined,
    {
      memorySize: 256,
      timeout: 60,
    }
  );

  addLambdaPermissionForSQS('playerHandler', playerLambda, config.playerQueue);

  // Create an SNS topic for CloudWatch alarms
  const errorAlarmTopic = createCloudWatchAlarmTopic('player-error');

  // Add SQS Queue Event Source Mapping to Lambda with monitoring
  createMonitoredEventSourceMapping(
    'playerHandler',
    playerLambda,
    config.playerQueue,
    config.playerDLQ,
    10, // batchSize
    sqsBatchWindowMinimum, // maximumBatchingWindowInSeconds
    {
      functionResponseTypes: ['ReportBatchItemFailures'],
    }, // additionalConfig
    [errorAlarmTopic.arn] // alarmActions
  );

  const unattachedPlayersQueueRole = addQueueReadPolicyToRole(
    'unattachedPlayersQueue',
    config.unattachedPlayersQueue
  );

  const [unattachedPlayersLambda, unattachedPlayersLogGroup] = createLambdaFunction(
    'unattachedPlayersHandler',
    '../dist/generate/unattached-players',
    'index.handler',
    {
      QUEUE_URL: config.unattachedPlayersQueue.url,
    },
    unattachedPlayersQueueRole,
    undefined,
    {
      memorySize: 256,
    }
  );

  addLambdaPermissionForSQS(
    'unattachedPlayersHandler',
    unattachedPlayersLambda,
    config.unattachedPlayersQueue
  );

  // Add SQS Queue Event Source Mapping to Lambda with monitoring
  createMonitoredEventSourceMapping(
    'unattachedPlayersHandler',
    unattachedPlayersLambda,
    config.unattachedPlayersQueue,
    config.unattachedPlayersDLQ,
    10, // batchSize
    sqsBatchWindowMaximum, // maximumBatchingWindowInSeconds
    undefined, // additionalConfig
    [errorAlarmTopic.arn] // alarmActions
  );

  const [getTransferListPlayersLambda] = createLambdaFunction(
    'getTransferListPlayersHandler',
    '../dist/player/getTransferListPlayers',
    'index.handler',
    undefined,
    undefined,
    undefined,
    {
      memorySize: 256,
      timeout: 30,
    }
  );

  // My Bid Transfer List Players API
  const [getMyBidTransferListPlayersLambda] = createLambdaFunction(
    'getMyBidTransferListPlayersHandler',
    '../dist/player/getMyBidTransferListPlayers',
    'index.handler'
  );

  const [useMagicSpongeLambda] = createLambdaFunction(
    'useMagicSpongeHandler',
    '../dist/player/useMagicSponge',
    'index.handler'
  );

  return {
    playerLambda,
    unattachedPlayersLambda,
    getTransferListPlayersLambda,
    getMyBidTransferListPlayersLambda,
    useMagicSpongeLambda,
    errorAlarmTopic,
  };
}
