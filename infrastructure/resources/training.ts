import { sqsBatchWindowMaximum } from '../config';
import { createScheduledRule } from '../eventBridge';
import { createLambdaFunction } from '../lambda';
import {
  addLambdaPermissionForSQS,
  addQueueReadPolicyToRole,
  addQueueSendPolicyToRole,
  createDLQ,
  createQueue,
} from '../queue';
import { createCloudWatchAlarmTopic, createMonitoredEventSourceMapping } from '../queueMonitoring';

interface TrainingResourcesConfig {}

export function createTrainingResources(config: TrainingResourcesConfig) {
  const [getTrainingSlotsLambda] = createLambdaFunction(
    'getTrainingSlots',
    '../dist/training/getTrainingSlots',
    'index.handler'
  );

  const [assignTrainingSlotLambda] = createLambdaFunction(
    'assignTrainingSlot',
    '../dist/training/assignTrainingSlot',
    'index.handler'
  );

  const [unlockTrainingSlotLambda] = createLambdaFunction(
    'unlockTrainingSlot',
    '../dist/training/unlockTrainingSlot',
    'index.handler'
  );

  // Create SQS Queues and DLQs for each type
  const trainingDLQ = createDLQ('trainingQueue');
  const trainingQueue = createQueue('trainingQueue', trainingDLQ, 3, {
    visibilityTimeout: 300,
  });

  let writeRole = addQueueSendPolicyToRole('scheduleTrainingImprovement', trainingQueue);
  const [scheduleTrainingImprovementLambda, scheduleTrainingImprovementLogGroup] =
    createLambdaFunction(
      'scheduleTrainingImprovement',
      '../dist/training/scheduleTrainingImprovement',
      'index.handler',
      {
        TRAINING_QUEUE_URL: trainingQueue.url,
      },
      writeRole,
      undefined,
      {
        memorySize: 256,
        timeout: 30,
      }
    );

  // Schedule to run at 12am and 12pm
  createScheduledRule({
    name: 'schedule-training-improvement-12pm',
    description: 'Trigger training improvement at 12pm every day',
    scheduleExpression: 'cron(0 12 * * ? *)',
    lambda: scheduleTrainingImprovementLambda,
  });

  createScheduledRule({
    name: 'schedule-training-improvement-8am',
    description: 'Trigger training improvement at 12am every day',
    scheduleExpression: 'cron(0 0 * * ? *)',
    lambda: scheduleTrainingImprovementLambda,
  });

  let readRole = addQueueReadPolicyToRole('processTrainingImprovement', trainingQueue);
  const [processTrainingImprovementLambda, processTrainingImprovementLogGroup] =
    createLambdaFunction(
      'processTrainingImprovement',
      '../dist/training/processTrainingImprovements',
      'index.handler',
      {
        TRAINING_QUEUE_URL: trainingQueue.url,
      },
      readRole,
      undefined,
      {
        memorySize: 256,
        timeout: 60,
      }
    );

  // Allow SQS to invoke the reader Lambda
  addLambdaPermissionForSQS(
    'processTrainingImprovement',
    processTrainingImprovementLambda,
    trainingQueue
  );

  // Create an SNS topic for CloudWatch alarms
  const errorAlarmTopic = createCloudWatchAlarmTopic('training-error');

  // Event source mapping to trigger Lambda from queue
  createMonitoredEventSourceMapping(
    'processTrainingImprovement',
    processTrainingImprovementLambda,
    trainingQueue,
    trainingDLQ,
    10, // batchSize
    sqsBatchWindowMaximum, // maximumBatchingWindowInSeconds
    {},
    [errorAlarmTopic.arn] // alarmActions
  );

  return {
    getTrainingSlotsLambda,
    assignTrainingSlotLambda,
    unlockTrainingSlotLambda,
    errorAlarmTopic,
  };
}
