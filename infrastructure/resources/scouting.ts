import { Function } from '@pulumi/aws/lambda';
import { sqsBatchWindowMaximum } from '../config';
import { createScheduledRule } from '../eventBridge';
import { addLambdaInvokePolicyToRole, createLambdaFunction } from '../lambda';
import {
  addLambdaPermissionForSQS,
  addQueueReadPolicyToRole,
  createDLQ,
  createQueue,
  createQueuePolicy,
  subscribeQueueToTopic,
} from '../queue';
import { createCloudWatchAlarmTopic, createMonitoredEventSourceMapping } from '../queueMonitoring';
import { addTopicPolicyToRole, createTopic } from '../topic';

export function createScoutingResources({ getManagerLambda }: { getManagerLambda: Function }) {
  // Create SNS Topic for scouting
  const scoutingTopic = createTopic('scoutingTopic');

  // Create SQS Queues and DLQs for each type
  const playerScoutingDLQ = createDLQ('playerScoutingQueue');
  const playerScoutingQueue = createQueue('playerScoutingQueue', playerScoutingDLQ, 3, {
    visibilityTimeout: 300,
  });

  const teamScoutingDLQ = createDLQ('teamScoutingQueue');
  const teamScoutingQueue = createQueue('teamScoutingQueue', teamScoutingDLQ, 3, {
    visibilityTimeout: 300,
  });

  const leagueScoutingDLQ = createDLQ('leagueScoutingQueue');
  const leagueScoutingQueue = createQueue('leagueScoutingQueue', leagueScoutingDLQ, 3, {
    visibilityTimeout: 300,
  });

  // Create queue policies and subscribe to SNS topic with filters
  createQueuePolicy('playerScoutingQueue', playerScoutingQueue, scoutingTopic.arn);
  createQueuePolicy('teamScoutingQueue', teamScoutingQueue, scoutingTopic.arn);
  createQueuePolicy('leagueScoutingQueue', leagueScoutingQueue, scoutingTopic.arn);

  const playerQueueRole = addQueueReadPolicyToRole('playerScoutingQueue', playerScoutingQueue);
  const teamQueueRole = addQueueReadPolicyToRole('teamScoutingQueue', teamScoutingQueue);
  const leagueQueueRole = addQueueReadPolicyToRole('leagueScoutingQueue', leagueScoutingQueue);

  // Subscribe queues to topic with type-specific filters
  subscribeQueueToTopic('playerScoutingQueue', playerScoutingQueue, scoutingTopic, {
    DataType: ['player'],
  });
  subscribeQueueToTopic('teamScoutingQueue', teamScoutingQueue, scoutingTopic, {
    DataType: ['team'],
  });
  subscribeQueueToTopic('leagueScoutingQueue', leagueScoutingQueue, scoutingTopic, {
    DataType: ['league'],
  });

  // Add SNS publish permissions to the role
  let queueScoutingRequestsRole = addTopicPolicyToRole(
    'queueScoutingRequestsHandler',
    scoutingTopic
  );

  // Create the Lambda function for queueing requests
  const [queueScoutingRequestsLambda] = createLambdaFunction(
    'queueScoutingRequestsHandler',
    '../dist/scouting/queueScoutingRequests',
    'index.handler',
    {
      SCOUTING_TOPIC_ARN: scoutingTopic.arn,
    },
    queueScoutingRequestsRole,
    undefined,
    {
      memorySize: 256,
      timeout: 60,
    }
  );

  const [processPlayerScoutingLambda, processPlayerScoutingLogGroup] = createLambdaFunction(
    'processPlayerScoutingHandler',
    '../dist/scouting/processPlayerScouting',
    'index.handler',
    {
      QUEUE_URL: playerScoutingQueue.url,
    },
    playerQueueRole,
    undefined,
    {
      memorySize: 512,
      timeout: 300,
    }
  );

  const [processTeamScoutingLambda, processTeamScoutingLogGroup] = createLambdaFunction(
    'processTeamScoutingHandler',
    '../dist/scouting/processTeamScouting',
    'index.handler',
    {
      QUEUE_URL: teamScoutingQueue.url,
    },
    teamQueueRole,
    undefined,
    {
      memorySize: 512,
      timeout: 300,
    }
  );

  const [processLeagueScoutingLambda, processLeagueScoutingLogGroup] = createLambdaFunction(
    'processLeagueScoutingHandler',
    '../dist/scouting/processLeagueScouting',
    'index.handler',
    {
      QUEUE_URL: leagueScoutingQueue.url,
    },
    leagueQueueRole,
    undefined,
    {
      memorySize: 512,
      timeout: 300,
    }
  );

  // Add Lambda permissions and event source mappings
  addLambdaPermissionForSQS(
    'processPlayerScoutingHandler',
    processPlayerScoutingLambda,
    playerScoutingQueue
  );
  addLambdaPermissionForSQS(
    'processTeamScoutingHandler',
    processTeamScoutingLambda,
    teamScoutingQueue
  );
  addLambdaPermissionForSQS(
    'processLeagueScoutingHandler',
    processLeagueScoutingLambda,
    leagueScoutingQueue
  );

  // Create an SNS topic for CloudWatch alarms
  const errorAlarmTopic = createCloudWatchAlarmTopic('scouting-error');

  // Create monitored event source mappings that will alarm on DLQ messages
  createMonitoredEventSourceMapping(
    'processPlayerScoutingHandler',
    processPlayerScoutingLambda,
    playerScoutingQueue,
    playerScoutingDLQ,
    10,
    sqsBatchWindowMaximum,
    undefined,
    [errorAlarmTopic.arn]
  );
  createMonitoredEventSourceMapping(
    'processTeamScoutingHandler',
    processTeamScoutingLambda,
    teamScoutingQueue,
    teamScoutingDLQ,
    10,
    sqsBatchWindowMaximum,
    undefined,
    [errorAlarmTopic.arn]
  );
  createMonitoredEventSourceMapping(
    'processLeagueScoutingHandler',
    processLeagueScoutingLambda,
    leagueScoutingQueue,
    leagueScoutingDLQ,
    10,
    sqsBatchWindowMaximum,
    undefined,
    [errorAlarmTopic.arn]
  );

  createScheduledRule({
    name: 'queue-scouting-requests',
    description: 'Trigger scouting requests queueing every 2 hours',
    scheduleExpression: 'rate(2 hours)',
    lambda: queueScoutingRequestsLambda,
  });

  const [requestScoutingLambda] = createLambdaFunction(
    'requestScoutingHandler',
    '../dist/scouting/requestScouting',
    'index.handler'
  );

  // Create Lambda for getting scouted players
  // Add permission to invoke the getManager Lambda
  let getScoutedPlayersRole = addLambdaInvokePolicyToRole('getScoutedPlayersHandler', [
    getManagerLambda,
  ]);

  const [getScoutedPlayersLambda] = createLambdaFunction(
    'getScoutedPlayersHandler',
    '../dist/scouting/getScoutedPlayers',
    'index.handler',
    undefined,
    getScoutedPlayersRole,
    undefined,
    {
      memorySize: 256,
      timeout: 30,
    }
  );

  return {
    scoutingTopic,
    playerScoutingQueue,
    teamScoutingQueue,
    leagueScoutingQueue,
    requestScoutingLambda,
    queueScoutingRequestsLambda,
    processPlayerScoutingLambda,
    processTeamScoutingLambda,
    processLeagueScoutingLambda,
    getScoutedPlayersLambda,
    errorAlarmTopic,
  };
}
